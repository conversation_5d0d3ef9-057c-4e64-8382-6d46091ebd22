import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform, Dimensions } from 'react-native';
import * as Device from 'expo-device';
import Constants from 'expo-constants';

// Log levels
export const LOG_LEVELS = {
  DEBUG: 'DEBUG',
  INFO: 'INFO',
  WARN: 'WARN',
  ERROR: 'ERROR',
  FATAL: 'FATAL',
};

// Storage key for logs
const LOGS_STORAGE_KEY = '@nutrition_tracker:logs';

// Maximum number of logs to keep
const MAX_LOGS = 100;

/**
 * Get device information for logging context
 */
export const getDeviceInfo = async () => {
  try {
    return {
      deviceName: Device.deviceName || 'Unknown',
      deviceYearClass: await Device.getDeviceYearClassAsync() || 'Unknown',
      osName: Device.osName || 'Unknown',
      osVersion: Device.osVersion || 'Unknown',
      manufacturer: Device.manufacturer || 'Unknown',
      modelName: Device.modelName || 'Unknown',
      screenSize: `${Dimensions.get('window').width}x${Dimensions.get('window').height}`,
      platform: Platform.OS,
      platformVersion: Platform.Version,
      expoVersion: Constants.expoVersion || 'Unknown',
      appVersion: Constants.manifest?.version || 'Unknown',
      isDevice: Device.isDevice,
    };
  } catch (error) {
    console.error('Error getting device info:', error);
    return {
      error: 'Failed to get device info',
      platform: Platform.OS,
    };
  }
};

/**
 * Log a message with specified level and optional metadata
 * @param {string} level - Log level (DEBUG, INFO, WARN, ERROR, FATAL)
 * @param {string} message - Log message
 * @param {Object} metadata - Additional metadata
 */
export const log = async (level, message, metadata = {}) => {
  try {
    // Create log entry
    const logEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      metadata,
    };
    
    // Log to console
    switch (level) {
      case LOG_LEVELS.DEBUG:
        console.debug(message, metadata);
        break;
      case LOG_LEVELS.INFO:
        console.info(message, metadata);
        break;
      case LOG_LEVELS.WARN:
        console.warn(message, metadata);
        break;
      case LOG_LEVELS.ERROR:
      case LOG_LEVELS.FATAL:
        console.error(message, metadata);
        break;
      default:
        console.log(message, metadata);
    }
    
    // Store log in AsyncStorage
    const storedLogsJson = await AsyncStorage.getItem(LOGS_STORAGE_KEY);
    const storedLogs = storedLogsJson ? JSON.parse(storedLogsJson) : [];
    
    // Add new log and limit size
    const updatedLogs = [logEntry, ...storedLogs].slice(0, MAX_LOGS);
    
    await AsyncStorage.setItem(LOGS_STORAGE_KEY, JSON.stringify(updatedLogs));
    
    return true;
  } catch (error) {
    console.error('Error logging message:', error);
    return false;
  }
};

/**
 * Get all stored logs
 * @returns {Promise<Array>} - Array of log entries
 */
export const getLogs = async () => {
  try {
    const logsJson = await AsyncStorage.getItem(LOGS_STORAGE_KEY);
    return logsJson ? JSON.parse(logsJson) : [];
  } catch (error) {
    console.error('Error getting logs:', error);
    return [];
  }
};

/**
 * Clear all stored logs
 * @returns {Promise<boolean>} - Success status
 */
export const clearLogs = async () => {
  try {
    await AsyncStorage.removeItem(LOGS_STORAGE_KEY);
    return true;
  } catch (error) {
    console.error('Error clearing logs:', error);
    return false;
  }
};

/**
 * Export logs as a string
 * @returns {Promise<string>} - Logs as a string
 */
export const exportLogs = async () => {
  try {
    const logs = await getLogs();
    const deviceInfo = await getDeviceInfo();
    
    const exportData = {
      deviceInfo,
      logs,
      exportedAt: new Date().toISOString(),
    };
    
    return JSON.stringify(exportData, null, 2);
  } catch (error) {
    console.error('Error exporting logs:', error);
    return JSON.stringify({ error: 'Failed to export logs' });
  }
};

/**
 * Log an error with stack trace and context
 * @param {Error} error - Error object
 * @param {string} context - Context where the error occurred
 * @param {Object} additionalData - Additional data about the error
 */
export const logError = async (error, context, additionalData = {}) => {
  try {
    const errorData = {
      name: error.name,
      message: error.message,
      stack: error.stack,
      context,
      ...additionalData,
    };
    
    await log(LOG_LEVELS.ERROR, `Error in ${context}: ${error.message}`, errorData);
    
    // For critical errors, also capture device info
    if (additionalData.critical) {
      const deviceInfo = await getDeviceInfo();
      await log(LOG_LEVELS.ERROR, 'Device info for critical error', { deviceInfo });
    }
    
    return true;
  } catch (logError) {
    console.error('Error logging error:', logError);
    return false;
  }
};
