/**
 * Database Service for ZnüniZähler
 * Provides high-level database operations for the application
 */

import dbManager from '../database/DatabaseManager';
import {
  userDataAccess,
  foodDataAccess,
  consumptionDataAccess,
  nutrientDataAccess,
  ingredientDataAccess,
  mealTypeDataAccess
} from '../database/DataAccess';
import { v4 as uuidv4 } from 'uuid';
import foodDatabaseImporter from '../utils/FoodDatabaseImporter';

/**
 * Initialize the database
 * @returns {Promise<void>}
 */
export const initializeDatabase = async () => {
  try {
    await dbManager.init();
    console.log('Database initialized successfully');
  } catch (error) {
    console.error('Error initializing database:', error);
    throw error;
  }
};

/**
 * Get database statistics
 * @returns {Promise<Object>} - Database statistics
 */
export const getDatabaseStats = async () => {
  try {
    return await dbManager.getDatabaseStats();
  } catch (error) {
    console.error('Error getting database stats:', error);
    throw error;
  }
};

/**
 * Reset the database (use with caution)
 * @returns {Promise<void>}
 */
export const resetDatabase = async () => {
  try {
    await dbManager.resetDatabase();
    console.log('Database reset successfully');
  } catch (error) {
    console.error('Error resetting database:', error);
    throw error;
  }
};

/**
 * Backup the database
 * @returns {Promise<string>} - Backup file path
 */
export const backupDatabase = async () => {
  try {
    return await dbManager.backupDatabase();
  } catch (error) {
    console.error('Error backing up database:', error);
    throw error;
  }
};

/**
 * Get or create the current user
 * @returns {Promise<Object>} - User object
 */
export const getCurrentUser = async () => {
  try {
    let user = await userDataAccess.getCurrentUser();

    if (!user) {
      // Create default user
      user = await userDataAccess.create({
        id: uuidv4(),
        name: 'Default User',
        email: '<EMAIL>',
        dark_mode_enabled: 1,
        preferred_language: 'en',
        measurement_unit: 'metric'
      });
    }

    return user;
  } catch (error) {
    console.error('Error getting current user:', error);
    throw error;
  }
};

/**
 * Update user settings
 * @param {string} userId - User ID
 * @param {Object} settings - Settings to update
 * @returns {Promise<Object>} - Updated user
 */
export const updateUserSettings = async (userId, settings) => {
  try {
    const updateData = {};

    // Handle core user properties
    if (settings.name !== undefined) {
      updateData.name = settings.name;
    }

    if (settings.email !== undefined) {
      updateData.email = settings.email;
    }

    if (settings.darkModeEnabled !== undefined) {
      updateData.dark_mode_enabled = settings.darkModeEnabled ? 1 : 0;
    }

    if (settings.preferredLanguage !== undefined) {
      updateData.preferred_language = settings.preferredLanguage;
    }

    if (settings.measurementUnit !== undefined) {
      updateData.measurement_unit = settings.measurementUnit;
    }

    // Update user if there are core properties to update
    if (Object.keys(updateData).length > 0) {
      await userDataAccess.update(userId, updateData);
    }

    // Handle custom settings
    for (const [key, value] of Object.entries(settings)) {
      if (!['name', 'email', 'darkModeEnabled', 'preferredLanguage', 'measurementUnit'].includes(key)) {
        await userDataAccess.saveSetting(userId, key, value);
      }
    }

    return userDataAccess.getById(userId);
  } catch (error) {
    console.error('Error updating user settings:', error);
    throw error;
  }
};

/**
 * Get food by barcode
 * @param {string} barcode - Barcode
 * @returns {Promise<Object|null>} - Food object or null
 */
export const getFoodByBarcode = async (barcode) => {
  try {
    const food = await foodDataAccess.getByBarcode(barcode);

    if (food) {
      // Get nutrients and ingredients
      food.nutrients = await foodDataAccess.getNutrients(food.id);
      food.ingredients = await foodDataAccess.getIngredients(food.id);
    }

    return food;
  } catch (error) {
    console.error('Error getting food by barcode:', error);
    throw error;
  }
};

/**
 * Search foods by name
 * @param {string} query - Search query
 * @param {number} limit - Maximum number of results
 * @returns {Promise<Array>} - Array of food objects
 */
export const searchFoods = async (query, limit = 20) => {
  try {
    return await foodDataAccess.search('name', query, { limit });
  } catch (error) {
    console.error('Error searching foods:', error);
    throw error;
  }
};

/**
 * Save food
 * @param {Object} food - Food data
 * @returns {Promise<Object>} - Saved food
 */
export const saveFood = async (food) => {
  try {
    let savedFood;

    if (food.id) {
      // Update existing food
      savedFood = await foodDataAccess.update(food.id, food);
    } else {
      // Create new food
      food.id = uuidv4();
      savedFood = await foodDataAccess.create(food);
    }

    // Save nutrients if provided
    if (food.nutrients && food.nutrients.length > 0) {
      for (const nutrient of food.nutrients) {
        await foodDataAccess.saveNutrient(savedFood.id, nutrient.nutrient_id, nutrient.amount);
      }
    }

    // Get complete food with nutrients and ingredients
    savedFood.nutrients = await foodDataAccess.getNutrients(savedFood.id);
    savedFood.ingredients = await foodDataAccess.getIngredients(savedFood.id);

    return savedFood;
  } catch (error) {
    console.error('Error saving food:', error);
    throw error;
  }
};

/**
 * Get consumptions by date
 * @param {string} userId - User ID
 * @param {string} date - Date (YYYY-MM-DD)
 * @returns {Promise<Array>} - Array of consumption objects
 */
export const getConsumptionsByDate = async (userId, date) => {
  try {
    return await consumptionDataAccess.getByDate(userId, date);
  } catch (error) {
    console.error('Error getting consumptions by date:', error);
    throw error;
  }
};

/**
 * Save consumption
 * @param {Object} consumption - Consumption data
 * @returns {Promise<Object>} - Saved consumption
 */
export const saveConsumption = async (consumption) => {
  try {
    let savedConsumption;

    if (consumption.id) {
      // Update existing consumption
      savedConsumption = await consumptionDataAccess.update(consumption.id, consumption);
    } else {
      // Create new consumption
      consumption.id = uuidv4();
      savedConsumption = await consumptionDataAccess.create(consumption);
    }

    return savedConsumption;
  } catch (error) {
    console.error('Error saving consumption:', error);
    throw error;
  }
};

/**
 * Add consumption item
 * @param {string} consumptionId - Consumption ID
 * @param {string} foodId - Food ID
 * @param {number} quantity - Quantity
 * @param {string} unit - Unit
 * @returns {Promise<Object>} - Saved consumption item
 */
export const addConsumptionItem = async (consumptionId, foodId, quantity, unit = 'g') => {
  try {
    return await consumptionDataAccess.addItem(consumptionId, foodId, quantity, unit);
  } catch (error) {
    console.error('Error adding consumption item:', error);
    throw error;
  }
};

/**
 * Get daily nutrition summary
 * @param {string} userId - User ID
 * @param {string} date - Date (YYYY-MM-DD)
 * @returns {Promise<Object>} - Nutrition summary
 */
export const getDailyNutritionSummary = async (userId, date) => {
  try {
    return await consumptionDataAccess.getDailyNutritionSummary(userId, date);
  } catch (error) {
    console.error('Error getting daily nutrition summary:', error);
    throw error;
  }
};

/**
 * Get all nutrients
 * @returns {Promise<Array>} - Array of nutrients
 */
export const getAllNutrients = async () => {
  try {
    return await nutrientDataAccess.getAll();
  } catch (error) {
    console.error('Error getting all nutrients:', error);
    throw error;
  }
};

/**
 * Get all macronutrients
 * @returns {Promise<Array>} - Array of macronutrients
 */
export const getMacronutrients = async () => {
  try {
    return await nutrientDataAccess.getMacronutrients();
  } catch (error) {
    console.error('Error getting macronutrients:', error);
    throw error;
  }
};

/**
 * Get all meal types
 * @returns {Promise<Array>} - Array of meal types
 */
export const getMealTypes = async () => {
  try {
    return await mealTypeDataAccess.getAllOrdered();
  } catch (error) {
    console.error('Error getting meal types:', error);
    throw error;
  }
};

/**
 * Get all allergens
 * @returns {Promise<Array>} - Array of allergens
 */
export const getAllergens = async () => {
  try {
    return await ingredientDataAccess.getAllergens();
  } catch (error) {
    console.error('Error getting allergens:', error);
    throw error;
  }
};

/**
 * Get favorite foods
 * @returns {Promise<Array>} - Array of favorite foods
 */
export const getFavorites = async () => {
  try {
    return await foodDataAccess.getFavorites();
  } catch (error) {
    console.error('Error getting favorite foods:', error);
    throw error;
  }
};

/**
 * Get custom foods
 * @returns {Promise<Array>} - Array of custom foods
 */
export const getCustomFoods = async () => {
  try {
    return await foodDataAccess.getCustomFoods();
  } catch (error) {
    console.error('Error getting custom foods:', error);
    throw error;
  }
};

/**
 * Get nutrients
 * @returns {Promise<Array>} - Array of nutrients
 */
export const getNutrients = async () => {
  try {
    return await nutrientDataAccess.getAll();
  } catch (error) {
    console.error('Error getting nutrients:', error);
    throw error;
  }
};

/**
 * Check if a food database is imported
 * @param {string} source - Database source (e.g., 'USDA', 'FoodB')
 * @returns {Promise<boolean>} - Whether the database is imported
 */
export const isFoodDatabaseImported = async (source) => {
  try {
    return await dbManager.isFoodDatabaseImported(source);
  } catch (error) {
    console.error(`Error checking if ${source} database is imported:`, error);
    return false;
  }
};

/**
 * Import a food database
 * @param {string} databaseName - Name of the database file (without extension)
 * @param {Function} progressCallback - Callback function for progress updates
 * @returns {Promise<Object>} - Import statistics
 */
export const importFoodDatabase = async (databaseName, progressCallback = null) => {
  try {
    return await foodDatabaseImporter.importFoodDatabase(databaseName, progressCallback);
  } catch (error) {
    console.error(`Error importing ${databaseName} database:`, error);
    throw error;
  }
};

/**
 * Get foods by source
 * @param {string} source - Database source (e.g., 'USDA', 'FoodB')
 * @param {number} limit - Maximum number of results
 * @param {number} offset - Offset for pagination
 * @returns {Promise<Array>} - Array of food objects
 */
export const getFoodsBySource = async (source, limit = 20, offset = 0) => {
  try {
    return await foodDataAccess.getBySource(source, limit, offset);
  } catch (error) {
    console.error(`Error getting foods by source ${source}:`, error);
    throw error;
  }
};

export default {
  initializeDatabase,
  getDatabaseStats,
  resetDatabase,
  backupDatabase,
  getCurrentUser,
  updateUserSettings,
  getFoodByBarcode,
  searchFoods,
  saveFood,
  getConsumptionsByDate,
  saveConsumption,
  addConsumptionItem,
  getDailyNutritionSummary,
  getAllNutrients,
  getMacronutrients,
  getMealTypes,
  getAllergens,
  getFavorites,
  getCustomFoods,
  getNutrients,
  isFoodDatabaseImported,
  importFoodDatabase,
  getFoodsBySource
};
