import AsyncStorage from '@react-native-async-storage/async-storage';

// Storage keys
const FOOD_ENTRIES_KEY = '@nutrition_tracker:food_entries';
const USER_PROFILE_KEY = '@nutrition_tracker:user_profile';
const WARNING_INGREDIENTS_KEY = '@nutrition_tracker:warning_ingredients';

/**
 * Save a food entry to local storage
 * @param {Object} entry - Food entry object
 * @returns {Promise}
 */
export const saveFoodEntry = async (entry) => {
  try {
    // Generate a unique ID for the entry
    const entryWithId = {
      ...entry,
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
    };
    
    // Get existing entries
    const existingEntriesJson = await AsyncStorage.getItem(FOOD_ENTRIES_KEY);
    const existingEntries = existingEntriesJson ? JSON.parse(existingEntriesJson) : [];
    
    // Add new entry
    const updatedEntries = [...existingEntries, entryWithId];
    
    // Save updated entries
    await AsyncStorage.setItem(FOOD_ENTRIES_KEY, JSON.stringify(updatedEntries));
    
    return {
      success: true,
      data: entryWithId,
    };
  } catch (error) {
    console.error('Error saving food entry:', error);
    return {
      success: false,
      error: error.message || 'Failed to save food entry',
    };
  }
};

/**
 * Get all food entries
 * @returns {Promise} - Promise with array of food entries
 */
export const getAllFoodEntries = async () => {
  try {
    const entriesJson = await AsyncStorage.getItem(FOOD_ENTRIES_KEY);
    const entries = entriesJson ? JSON.parse(entriesJson) : [];
    
    return {
      success: true,
      data: entries,
    };
  } catch (error) {
    console.error('Error getting food entries:', error);
    return {
      success: false,
      error: error.message || 'Failed to get food entries',
    };
  }
};

/**
 * Get food entries for a specific date
 * @param {string} date - Date string in ISO format (YYYY-MM-DD)
 * @returns {Promise} - Promise with array of food entries for the date
 */
export const getFoodEntriesByDate = async (date) => {
  try {
    const { success, data, error } = await getAllFoodEntries();
    
    if (!success) {
      return { success, error };
    }
    
    // Filter entries by date
    const entriesForDate = data.filter(entry => {
      const entryDate = new Date(entry.timestamp).toISOString().split('T')[0];
      return entryDate === date;
    });
    
    return {
      success: true,
      data: entriesForDate,
    };
  } catch (error) {
    console.error('Error getting food entries by date:', error);
    return {
      success: false,
      error: error.message || 'Failed to get food entries by date',
    };
  }
};

/**
 * Save user profile
 * @param {Object} profile - User profile object
 * @returns {Promise}
 */
export const saveUserProfile = async (profile) => {
  try {
    await AsyncStorage.setItem(USER_PROFILE_KEY, JSON.stringify(profile));
    
    return {
      success: true,
      data: profile,
    };
  } catch (error) {
    console.error('Error saving user profile:', error);
    return {
      success: false,
      error: error.message || 'Failed to save user profile',
    };
  }
};

/**
 * Get user profile
 * @returns {Promise} - Promise with user profile
 */
export const getUserProfile = async () => {
  try {
    const profileJson = await AsyncStorage.getItem(USER_PROFILE_KEY);
    const profile = profileJson ? JSON.parse(profileJson) : null;
    
    return {
      success: true,
      data: profile,
    };
  } catch (error) {
    console.error('Error getting user profile:', error);
    return {
      success: false,
      error: error.message || 'Failed to get user profile',
    };
  }
};

/**
 * Save warning ingredients
 * @param {Array} ingredients - Array of warning ingredients
 * @returns {Promise}
 */
export const saveWarningIngredients = async (ingredients) => {
  try {
    await AsyncStorage.setItem(WARNING_INGREDIENTS_KEY, JSON.stringify(ingredients));
    
    return {
      success: true,
      data: ingredients,
    };
  } catch (error) {
    console.error('Error saving warning ingredients:', error);
    return {
      success: false,
      error: error.message || 'Failed to save warning ingredients',
    };
  }
};

/**
 * Get warning ingredients
 * @returns {Promise} - Promise with array of warning ingredients
 */
export const getWarningIngredients = async () => {
  try {
    const ingredientsJson = await AsyncStorage.getItem(WARNING_INGREDIENTS_KEY);
    const ingredients = ingredientsJson ? JSON.parse(ingredientsJson) : [];
    
    return {
      success: true,
      data: ingredients,
    };
  } catch (error) {
    console.error('Error getting warning ingredients:', error);
    return {
      success: false,
      error: error.message || 'Failed to get warning ingredients',
    };
  }
};
