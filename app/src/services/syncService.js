/**
 * Sync Service for ZnüniZähler
 * Provides functionality for data synchronization
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import * as FileSystem from 'expo-file-system';
import { getCurrentUser, updateUserSettings } from './databaseService';
import dbManager from '../database/DatabaseManager';

// Constants
const SYNC_TIMESTAMP_KEY = 'last_sync_timestamp';
const SYNC_INTERVAL = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

/**
 * Check if sync is needed
 * @returns {Promise<boolean>} - Whether sync is needed
 */
export const isSyncNeeded = async () => {
  try {
    // Get last sync timestamp
    const lastSyncStr = await AsyncStorage.getItem(SYNC_TIMESTAMP_KEY);
    
    if (!lastSyncStr) {
      return true; // No previous sync, sync needed
    }
    
    const lastSync = new Date(lastSyncStr);
    const now = new Date();
    
    // Check if sync interval has passed
    return now.getTime() - lastSync.getTime() > SYNC_INTERVAL;
  } catch (error) {
    console.error('Error checking sync status:', error);
    return false; // Default to no sync needed on error
  }
};

/**
 * Sync data with cloud
 * @returns {Promise<Object>} - Sync result
 */
export const syncData = async () => {
  try {
    // Get current user
    const user = await getCurrentUser();
    
    if (!user) {
      throw new Error('No user found');
    }
    
    // Backup database before sync
    const backupPath = await backupDatabaseBeforeSync();
    
    // Perform sync operations
    // This is a placeholder for actual sync implementation
    // In a real app, you would:
    // 1. Upload local changes to the server
    // 2. Download remote changes from the server
    // 3. Merge changes and resolve conflicts
    
    // For now, we'll just update the last sync timestamp
    const now = new Date().toISOString();
    await AsyncStorage.setItem(SYNC_TIMESTAMP_KEY, now);
    
    // Update user's last sync timestamp
    await updateUserSettings(user.id, { last_sync: now });
    
    return {
      success: true,
      timestamp: now,
      message: 'Data synchronized successfully',
      backupPath
    };
  } catch (error) {
    console.error('Error syncing data:', error);
    throw error;
  }
};

/**
 * Backup database before sync
 * @returns {Promise<string>} - Backup file path
 */
export const backupDatabaseBeforeSync = async () => {
  try {
    return await dbManager.backupDatabase();
  } catch (error) {
    console.error('Error backing up database before sync:', error);
    throw error;
  }
};

/**
 * Restore database from backup
 * @param {string} backupPath - Path to backup file
 * @returns {Promise<boolean>} - Success flag
 */
export const restoreFromBackup = async (backupPath) => {
  try {
    // Close current database connection
    dbManager.close();
    
    // Get database file path
    const dbDir = FileSystem.documentDirectory + 'SQLite/';
    const dbPath = dbDir + 'znunizaehler.db';
    
    // Copy backup to database file
    await FileSystem.copyAsync({
      from: backupPath,
      to: dbPath
    });
    
    // Reinitialize database
    await dbManager.init();
    
    return true;
  } catch (error) {
    console.error('Error restoring from backup:', error);
    throw error;
  }
};

/**
 * Get all available backups
 * @returns {Promise<Array<Object>>} - Array of backup objects
 */
export const getAvailableBackups = async () => {
  try {
    const backupDir = FileSystem.documentDirectory;
    const files = await FileSystem.readDirectoryAsync(backupDir);
    
    // Filter backup files
    const backupFiles = files.filter(file => file.startsWith('backup_') && file.endsWith('.db'));
    
    // Get file info
    const backups = await Promise.all(
      backupFiles.map(async (file) => {
        const fileInfo = await FileSystem.getInfoAsync(backupDir + file);
        
        // Parse timestamp from filename
        const timestampStr = file.replace('backup_', '').replace('.db', '');
        const timestamp = new Date(timestampStr.replace(/_/g, ':').replace(/T/g, ' '));
        
        return {
          filename: file,
          path: fileInfo.uri,
          size: fileInfo.size,
          timestamp
        };
      })
    );
    
    // Sort by timestamp (newest first)
    return backups.sort((a, b) => b.timestamp - a.timestamp);
  } catch (error) {
    console.error('Error getting available backups:', error);
    throw error;
  }
};

/**
 * Delete backup file
 * @param {string} backupPath - Path to backup file
 * @returns {Promise<boolean>} - Success flag
 */
export const deleteBackup = async (backupPath) => {
  try {
    await FileSystem.deleteAsync(backupPath);
    return true;
  } catch (error) {
    console.error('Error deleting backup:', error);
    throw error;
  }
};

export default {
  isSyncNeeded,
  syncData,
  backupDatabaseBeforeSync,
  restoreFromBackup,
  getAvailableBackups,
  deleteBackup
};
