/**
 * Barcode Scanner Component for ZnüniZähler
 * Provides a camera view to scan food barcodes
 */

import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, Alert } from 'react-native';
import { BarCodeScanner } from 'expo-barcode-scanner';
import { Button, ActivityIndicator, Surface } from 'react-native-paper';
import { useTheme } from '../theme/ThemeProvider';
import { getFoodByBarcode, saveFood } from '../services/databaseService';

/**
 * Barcode Scanner Component
 * @param {Object} props - Component props
 * @param {Function} props.onScan - Callback function when a barcode is scanned
 * @param {Function} props.onClose - Callback function to close the scanner
 * @returns {JSX.Element} - Barcode scanner component
 */
const BarcodeScanner = ({ onScan, onClose }) => {
  const { theme } = useTheme();
  const [hasPermission, setHasPermission] = useState(null);
  const [scanned, setScanned] = useState(false);
  const [scanning, setScanning] = useState(false);
  
  // Request camera permission
  useEffect(() => {
    const getBarCodeScannerPermissions = async () => {
      const { status } = await BarCodeScanner.requestPermissionsAsync();
      setHasPermission(status === 'granted');
    };
    
    getBarCodeScannerPermissions();
  }, []);
  
  // Handle barcode scan
  const handleBarCodeScanned = async ({ type, data }) => {
    if (scanned || scanning) return;
    
    setScanned(true);
    setScanning(true);
    
    try {
      // Look up the barcode in the database
      const food = await getFoodByBarcode(data);
      
      if (food) {
        // Food found in database
        onScan(food);
      } else {
        // Food not found, ask user if they want to add it
        Alert.alert(
          'Food Not Found',
          `The barcode ${data} was not found in the database. Would you like to add it?`,
          [
            {
              text: 'Cancel',
              style: 'cancel',
              onPress: () => {
                setScanned(false);
                setScanning(false);
              }
            },
            {
              text: 'Add',
              onPress: async () => {
                // Create a new food item with the barcode
                const newFood = {
                  name: 'New Food',
                  description: '',
                  barcode: data,
                  brand: '',
                  serving_size: 100,
                  serving_unit: 'g',
                  is_custom: 1,
                  nutrients: [
                    { nutrient_id: 'nutrient-calories', amount: 0 },
                    { nutrient_id: 'nutrient-protein', amount: 0 },
                    { nutrient_id: 'nutrient-carbs', amount: 0 },
                    { nutrient_id: 'nutrient-fat', amount: 0 }
                  ]
                };
                
                const savedFood = await saveFood(newFood);
                onScan(savedFood);
              }
            }
          ]
        );
      }
    } catch (error) {
      console.error('Error scanning barcode:', error);
      Alert.alert('Error', 'Failed to process barcode. Please try again.');
      setScanned(false);
      setScanning(false);
    }
  };
  
  // Handle permission denied
  if (hasPermission === null) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.text, { color: theme.colors.text }]}>Requesting camera permission...</Text>
      </View>
    );
  }
  
  if (hasPermission === false) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.text, { color: theme.colors.text }]}>No access to camera</Text>
        <Button mode="contained" onPress={onClose} style={styles.button}>
          Close
        </Button>
      </View>
    );
  }
  
  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <BarCodeScanner
        onBarCodeScanned={scanned ? undefined : handleBarCodeScanned}
        style={styles.scanner}
      />
      
      <Surface style={[styles.overlay, { backgroundColor: theme.colors.backdrop }]}>
        <Text style={[styles.instructions, { color: theme.colors.text }]}>
          Position the barcode within the frame to scan
        </Text>
        
        {scanning && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={[styles.text, { color: theme.colors.text }]}>Processing...</Text>
          </View>
        )}
        
        <Button 
          mode="contained" 
          onPress={() => {
            setScanned(false);
            onClose();
          }}
          style={styles.button}
          disabled={scanning}
        >
          Cancel
        </Button>
      </Surface>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanner: {
    ...StyleSheet.absoluteFillObject,
  },
  overlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    alignItems: 'center',
  },
  instructions: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
  },
  button: {
    marginTop: 20,
    width: '80%',
  },
  loadingContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  text: {
    fontSize: 16,
    marginTop: 10,
    textAlign: 'center',
  },
});

export default BarcodeScanner;
