/**
 * Tests for Data Access Layer
 */

import { 
  BaseDataAccess,
  userDataAccess,
  foodDataAccess,
  consumptionDataAccess,
  nutrientDataAccess,
  ingredientDataAccess,
  mealTypeDataAccess
} from '../../database/DataAccess';
import dbManager from '../../database/DatabaseManager';

// Mock the database manager
jest.mock('../../database/DatabaseManager', () => ({
  executeQuery: jest.fn(),
  executeTransaction: jest.fn()
}));

describe('BaseDataAccess', () => {
  let baseDataAccess;

  beforeEach(() => {
    baseDataAccess = new BaseDataAccess('TestTable', 'id');
    jest.clearAllMocks();
  });

  test('should get all records', async () => {
    const mockItems = [
      { id: 'item1', name: 'Item 1' },
      { id: 'item2', name: 'Item 2' }
    ];

    dbManager.executeQuery.mockResolvedValue({
      rows: {
        length: mockItems.length,
        item: (index) => mockItems[index]
      }
    });

    const items = await baseDataAccess.getAll();

    expect(dbManager.executeQuery).toHaveBeenCalledWith(
      expect.stringContaining('SELECT * FROM TestTable WHERE is_active = 1 AND deleted_at IS NULL')
    );
    expect(items).toEqual(mockItems);
  });

  test('should get record by ID', async () => {
    const mockItem = { id: 'item1', name: 'Item 1' };

    dbManager.executeQuery.mockResolvedValue({
      rows: {
        length: 1,
        item: () => mockItem
      }
    });

    const item = await baseDataAccess.getById('item1');

    expect(dbManager.executeQuery).toHaveBeenCalledWith(
      expect.stringContaining('SELECT * FROM TestTable WHERE id = ?'),
      ['item1']
    );
    expect(item).toEqual(mockItem);
  });

  test('should create a new record', async () => {
    const newItem = { name: 'New Item' };
    const mockItem = { id: 'mock-uuid', name: 'New Item' };

    dbManager.executeQuery.mockImplementation((query) => {
      if (query.includes('INSERT INTO')) {
        return Promise.resolve({ rowsAffected: 1 });
      } else {
        return Promise.resolve({
          rows: {
            length: 1,
            item: () => mockItem
          }
        });
      }
    });

    const item = await baseDataAccess.create(newItem);

    expect(dbManager.executeQuery).toHaveBeenCalledWith(
      expect.stringContaining('INSERT INTO TestTable'),
      expect.any(Array)
    );
    expect(item).toEqual(mockItem);
  });

  test('should update a record', async () => {
    const updateData = { name: 'Updated Item' };
    const mockItem = { id: 'item1', name: 'Updated Item' };

    dbManager.executeQuery.mockImplementation((query) => {
      if (query.includes('UPDATE')) {
        return Promise.resolve({ rowsAffected: 1 });
      } else {
        return Promise.resolve({
          rows: {
            length: 1,
            item: () => mockItem
          }
        });
      }
    });

    const item = await baseDataAccess.update('item1', updateData);

    expect(dbManager.executeQuery).toHaveBeenCalledWith(
      expect.stringContaining('UPDATE TestTable SET'),
      expect.arrayContaining(['Updated Item', 'item1'])
    );
    expect(item).toEqual(mockItem);
  });

  test('should delete a record', async () => {
    dbManager.executeQuery.mockResolvedValue({ rowsAffected: 1 });

    const result = await baseDataAccess.delete('item1');

    expect(dbManager.executeQuery).toHaveBeenCalledWith(
      expect.stringContaining('UPDATE TestTable SET is_active = 0, deleted_at = datetime'),
      ['item1']
    );
    expect(result).toBe(true);
  });

  test('should find records by field value', async () => {
    const mockItems = [
      { id: 'item1', category: 'A', name: 'Item 1' },
      { id: 'item2', category: 'A', name: 'Item 2' }
    ];

    dbManager.executeQuery.mockResolvedValue({
      rows: {
        length: mockItems.length,
        item: (index) => mockItems[index]
      }
    });

    const items = await baseDataAccess.findBy('category', 'A');

    expect(dbManager.executeQuery).toHaveBeenCalledWith(
      expect.stringContaining('SELECT * FROM TestTable WHERE category = ?'),
      ['A']
    );
    expect(items).toEqual(mockItems);
  });

  test('should search records by field value', async () => {
    const mockItems = [
      { id: 'item1', name: 'Apple' },
      { id: 'item2', name: 'Pineapple' }
    ];

    dbManager.executeQuery.mockResolvedValue({
      rows: {
        length: mockItems.length,
        item: (index) => mockItems[index]
      }
    });

    const items = await baseDataAccess.search('name', 'apple');

    expect(dbManager.executeQuery).toHaveBeenCalledWith(
      expect.stringContaining('SELECT * FROM TestTable WHERE name LIKE ?'),
      ['%apple%']
    );
    expect(items).toEqual(mockItems);
  });

  test('should count records', async () => {
    dbManager.executeQuery.mockResolvedValue({
      rows: {
        length: 1,
        item: () => ({ count: 5 })
      }
    });

    const count = await baseDataAccess.count({ category: 'A' });

    expect(dbManager.executeQuery).toHaveBeenCalledWith(
      expect.stringContaining('SELECT COUNT(*) as count FROM TestTable WHERE is_active = 1 AND deleted_at IS NULL AND category = ?'),
      ['A']
    );
    expect(count).toBe(5);
  });
});

describe('UserDataAccess', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should get user settings', async () => {
    const mockSettings = [
      { setting_key: 'theme', setting_value: 'dark' },
      { setting_key: 'notifications', setting_value: 'true' }
    ];

    dbManager.executeQuery.mockResolvedValue({
      rows: {
        length: mockSettings.length,
        item: (index) => mockSettings[index]
      }
    });

    const settings = await userDataAccess.getSettings('user1');

    expect(dbManager.executeQuery).toHaveBeenCalledWith(
      expect.stringContaining('SELECT setting_key, setting_value FROM UserSetting'),
      ['user1']
    );
    expect(settings).toEqual({
      theme: 'dark',
      notifications: 'true'
    });
  });

  test('should save user setting', async () => {
    // Mock existing setting
    dbManager.executeQuery.mockImplementation((query) => {
      if (query.includes('SELECT id FROM UserSetting')) {
        return Promise.resolve({
          rows: {
            length: 1,
            item: () => ({ id: 'setting1' })
          }
        });
      }
      return Promise.resolve({ rowsAffected: 1 });
    });

    await userDataAccess.saveSetting('user1', 'theme', 'light');

    expect(dbManager.executeQuery).toHaveBeenCalledWith(
      expect.stringContaining('UPDATE UserSetting SET setting_value = ?'),
      expect.arrayContaining(['light', 'user1', 'theme'])
    );

    // Mock new setting
    dbManager.executeQuery.mockImplementation((query) => {
      if (query.includes('SELECT id FROM UserSetting')) {
        return Promise.resolve({
          rows: {
            length: 0
          }
        });
      }
      return Promise.resolve({ rowsAffected: 1 });
    });

    await userDataAccess.saveSetting('user1', 'notifications', 'true');

    expect(dbManager.executeQuery).toHaveBeenCalledWith(
      expect.stringContaining('INSERT INTO UserSetting'),
      expect.arrayContaining(['mock-uuid', 'user1', 'notifications', 'true'])
    );
  });

  test('should get current user', async () => {
    const mockUser = {
      id: 'user1',
      name: 'Test User',
      email: '<EMAIL>'
    };

    dbManager.executeQuery.mockImplementation((query) => {
      if (query.includes('SELECT * FROM User')) {
        return Promise.resolve({
          rows: {
            length: 1,
            item: () => mockUser
          }
        });
      } else if (query.includes('SELECT setting_key, setting_value')) {
        return Promise.resolve({
          rows: {
            length: 0
          }
        });
      }
      return Promise.resolve({ rows: { length: 0 } });
    });

    const user = await userDataAccess.getCurrentUser();

    expect(dbManager.executeQuery).toHaveBeenCalledWith(
      expect.stringContaining('SELECT * FROM User WHERE is_active = 1 LIMIT 1')
    );
    expect(user).toEqual({
      id: 'user1',
      name: 'Test User',
      email: '<EMAIL>',
      settings: {}
    });
  });
});

describe('FoodDataAccess', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should get food by barcode', async () => {
    const mockFood = {
      id: 'food1',
      name: 'Test Food',
      barcode: '1234567890'
    };

    dbManager.executeQuery.mockResolvedValue({
      rows: {
        length: 1,
        item: () => mockFood
      }
    });

    const food = await foodDataAccess.getByBarcode('1234567890');

    expect(dbManager.executeQuery).toHaveBeenCalledWith(
      expect.stringContaining('SELECT * FROM Food WHERE barcode = ?'),
      ['1234567890']
    );
    expect(food).toEqual(mockFood);
  });

  test('should get food nutrients', async () => {
    const mockNutrients = [
      { id: 'fn1', food_id: 'food1', nutrient_id: 'nutrient1', amount: 100, name: 'Nutrient 1', unit: 'g', is_macro: 1 },
      { id: 'fn2', food_id: 'food1', nutrient_id: 'nutrient2', amount: 5, name: 'Nutrient 2', unit: 'mg', is_macro: 0 }
    ];

    dbManager.executeQuery.mockResolvedValue({
      rows: {
        length: mockNutrients.length,
        item: (index) => mockNutrients[index]
      }
    });

    const nutrients = await foodDataAccess.getNutrients('food1');

    expect(dbManager.executeQuery).toHaveBeenCalledWith(
      expect.stringContaining('SELECT fn.*, n.name, n.unit, n.is_macro'),
      ['food1']
    );
    expect(nutrients).toEqual(mockNutrients);
  });

  test('should save food nutrient', async () => {
    // Mock existing nutrient
    dbManager.executeQuery.mockImplementation((query) => {
      if (query.includes('SELECT id FROM FoodNutrient')) {
        return Promise.resolve({
          rows: {
            length: 1,
            item: () => ({ id: 'fn1' })
          }
        });
      }
      return Promise.resolve({ rowsAffected: 1 });
    });

    await foodDataAccess.saveNutrient('food1', 'nutrient1', 100);

    expect(dbManager.executeQuery).toHaveBeenCalledWith(
      expect.stringContaining('UPDATE FoodNutrient SET amount = ?'),
      expect.arrayContaining([100, 'food1', 'nutrient1'])
    );

    // Mock new nutrient
    dbManager.executeQuery.mockImplementation((query) => {
      if (query.includes('SELECT id FROM FoodNutrient')) {
        return Promise.resolve({
          rows: {
            length: 0
          }
        });
      }
      return Promise.resolve({ rowsAffected: 1 });
    });

    await foodDataAccess.saveNutrient('food1', 'nutrient2', 5);

    expect(dbManager.executeQuery).toHaveBeenCalledWith(
      expect.stringContaining('INSERT INTO FoodNutrient'),
      expect.arrayContaining(['mock-uuid', 'food1', 'nutrient2', 5])
    );
  });
});

describe('ConsumptionDataAccess', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should get consumption items', async () => {
    const mockItems = [
      { id: 'item1', consumption_id: 'consumption1', food_id: 'food1', quantity: 100, food_name: 'Food 1' },
      { id: 'item2', consumption_id: 'consumption1', food_id: 'food2', quantity: 200, food_name: 'Food 2' }
    ];

    dbManager.executeQuery.mockResolvedValue({
      rows: {
        length: mockItems.length,
        item: (index) => mockItems[index]
      }
    });

    const items = await consumptionDataAccess.getItems('consumption1');

    expect(dbManager.executeQuery).toHaveBeenCalledWith(
      expect.stringContaining('SELECT ci.*, f.name as food_name'),
      ['consumption1']
    );
    expect(items).toEqual(mockItems);
  });

  test('should add consumption item', async () => {
    const mockItem = {
      id: 'mock-uuid',
      consumption_id: 'consumption1',
      food_id: 'food1',
      quantity: 100,
      unit: 'g',
      food_name: 'Food 1'
    };

    dbManager.executeQuery.mockImplementation((query) => {
      if (query.includes('INSERT INTO')) {
        return Promise.resolve({ rowsAffected: 1 });
      } else {
        return Promise.resolve({
          rows: {
            length: 1,
            item: () => mockItem
          }
        });
      }
    });

    const item = await consumptionDataAccess.addItem('consumption1', 'food1', 100, 'g');

    expect(dbManager.executeQuery).toHaveBeenCalledWith(
      expect.stringContaining('INSERT INTO ConsumptionItem'),
      expect.arrayContaining(['mock-uuid', 'consumption1', 'food1', 100, 'g'])
    );
    expect(item).toEqual(mockItem);
  });

  test('should get consumptions by date', async () => {
    const mockConsumptions = [
      { id: 'consumption1', user_id: 'user1', consumption_date: '2023-01-01', meal_type: 'breakfast' },
      { id: 'consumption2', user_id: 'user1', consumption_date: '2023-01-01', meal_type: 'lunch' }
    ];

    const mockItems = [
      { id: 'item1', consumption_id: 'consumption1', food_id: 'food1', quantity: 100 },
      { id: 'item2', consumption_id: 'consumption2', food_id: 'food2', quantity: 200 }
    ];

    dbManager.executeQuery.mockImplementation((query) => {
      if (query.includes('SELECT c.*')) {
        return Promise.resolve({
          rows: {
            length: mockConsumptions.length,
            item: (index) => mockConsumptions[index]
          }
        });
      } else if (query.includes('SELECT ci.*')) {
        // Return different items based on consumption_id
        const consumptionId = query.includes('consumption1') ? 'consumption1' : 'consumption2';
        const filteredItems = mockItems.filter(item => item.consumption_id === consumptionId);
        return Promise.resolve({
          rows: {
            length: filteredItems.length,
            item: (index) => filteredItems[index]
          }
        });
      }
      return Promise.resolve({ rows: { length: 0 } });
    });

    const consumptions = await consumptionDataAccess.getByDate('user1', '2023-01-01');

    expect(dbManager.executeQuery).toHaveBeenCalledWith(
      expect.stringContaining('SELECT c.*'),
      ['user1', '2023-01-01']
    );
    expect(consumptions).toHaveLength(2);
    expect(consumptions[0].id).toBe('consumption1');
    expect(consumptions[1].id).toBe('consumption2');
    expect(consumptions[0].items).toBeDefined();
    expect(consumptions[1].items).toBeDefined();
  });
});
