/**
 * Tests for Database Service
 */

import * as databaseService from '../../services/databaseService';
import dbManager from '../../database/DatabaseManager';
import {
  userDataAccess,
  foodDataAccess,
  consumptionDataAccess,
  nutrientDataAccess,
  mealTypeDataAccess
} from '../../database/DataAccess';

// Mock the database manager
jest.mock('../../database/DatabaseManager', () => ({
  init: jest.fn().mockResolvedValue(),
  getDatabaseStats: jest.fn(),
  resetDatabase: jest.fn(),
  backupDatabase: jest.fn()
}));

// Mock the data access layer
jest.mock('../../database/DataAccess', () => ({
  userDataAccess: {
    getCurrentUser: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    getById: jest.fn(),
    saveSetting: jest.fn()
  },
  foodDataAccess: {
    getByBarcode: jest.fn(),
    getNutrients: jest.fn(),
    getIngredients: jest.fn(),
    search: jest.fn(),
    update: jest.fn(),
    create: jest.fn(),
    saveNutrient: jest.fn()
  },
  consumptionDataAccess: {
    getByDate: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    addItem: jest.fn(),
    getDailyNutritionSummary: jest.fn()
  },
  nutrientDataAccess: {
    getAll: jest.fn(),
    getMacronutrients: jest.fn()
  },
  mealTypeDataAccess: {
    getAllOrdered: jest.fn()
  },
  ingredientDataAccess: {
    getAllergens: jest.fn()
  }
}));

describe('Database Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should initialize the database', async () => {
    await databaseService.initializeDatabase();
    expect(dbManager.init).toHaveBeenCalled();
  });

  test('should get database stats', async () => {
    const mockStats = {
      version: 1,
      tables: [
        { name: 'User', records: 1 },
        { name: 'Food', records: 10 }
      ],
      totalRecords: 11
    };

    dbManager.getDatabaseStats.mockResolvedValue(mockStats);

    const stats = await databaseService.getDatabaseStats();

    expect(dbManager.getDatabaseStats).toHaveBeenCalled();
    expect(stats).toEqual(mockStats);
  });

  test('should reset the database', async () => {
    await databaseService.resetDatabase();
    expect(dbManager.resetDatabase).toHaveBeenCalled();
  });

  test('should backup the database', async () => {
    const mockBackupPath = '/path/to/backup.db';
    dbManager.backupDatabase.mockResolvedValue(mockBackupPath);

    const backupPath = await databaseService.backupDatabase();

    expect(dbManager.backupDatabase).toHaveBeenCalled();
    expect(backupPath).toBe(mockBackupPath);
  });

  test('should get current user', async () => {
    const mockUser = {
      id: 'user1',
      name: 'Test User',
      email: '<EMAIL>'
    };

    userDataAccess.getCurrentUser.mockResolvedValue(mockUser);

    const user = await databaseService.getCurrentUser();

    expect(userDataAccess.getCurrentUser).toHaveBeenCalled();
    expect(user).toEqual(mockUser);
  });

  test('should create default user when no user exists', async () => {
    const mockUser = {
      id: 'user1',
      name: 'Default User',
      email: '<EMAIL>'
    };

    userDataAccess.getCurrentUser.mockResolvedValue(null);
    userDataAccess.create.mockResolvedValue(mockUser);

    const user = await databaseService.getCurrentUser();

    expect(userDataAccess.getCurrentUser).toHaveBeenCalled();
    expect(userDataAccess.create).toHaveBeenCalled();
    expect(user).toEqual(mockUser);
  });

  test('should update user settings', async () => {
    const mockUser = {
      id: 'user1',
      name: 'Test User',
      email: '<EMAIL>'
    };

    userDataAccess.update.mockResolvedValue({ rowsAffected: 1 });
    userDataAccess.getById.mockResolvedValue(mockUser);

    const settings = {
      name: 'Updated User',
      email: '<EMAIL>',
      darkModeEnabled: true,
      theme: 'dark'
    };

    const user = await databaseService.updateUserSettings('user1', settings);

    expect(userDataAccess.update).toHaveBeenCalledWith('user1', expect.objectContaining({
      name: 'Updated User',
      email: '<EMAIL>',
      dark_mode_enabled: 1
    }));
    expect(userDataAccess.saveSetting).toHaveBeenCalledWith('user1', 'theme', 'dark');
    expect(userDataAccess.getById).toHaveBeenCalledWith('user1');
    expect(user).toEqual(mockUser);
  });

  test('should get food by barcode', async () => {
    const mockFood = {
      id: 'food1',
      name: 'Test Food',
      barcode: '1234567890'
    };

    const mockNutrients = [
      { id: 'fn1', food_id: 'food1', nutrient_id: 'nutrient1', amount: 100 }
    ];

    const mockIngredients = [
      { id: 'fi1', food_id: 'food1', ingredient_id: 'ingredient1', name: 'Ingredient 1' }
    ];

    foodDataAccess.getByBarcode.mockResolvedValue(mockFood);
    foodDataAccess.getNutrients.mockResolvedValue(mockNutrients);
    foodDataAccess.getIngredients.mockResolvedValue(mockIngredients);

    const food = await databaseService.getFoodByBarcode('1234567890');

    expect(foodDataAccess.getByBarcode).toHaveBeenCalledWith('1234567890');
    expect(foodDataAccess.getNutrients).toHaveBeenCalledWith('food1');
    expect(foodDataAccess.getIngredients).toHaveBeenCalledWith('food1');
    expect(food).toEqual({
      ...mockFood,
      nutrients: mockNutrients,
      ingredients: mockIngredients
    });
  });

  test('should search foods', async () => {
    const mockFoods = [
      { id: 'food1', name: 'Apple' },
      { id: 'food2', name: 'Pineapple' }
    ];

    foodDataAccess.search.mockResolvedValue(mockFoods);

    const foods = await databaseService.searchFoods('apple');

    expect(foodDataAccess.search).toHaveBeenCalledWith('name', 'apple', { limit: 20 });
    expect(foods).toEqual(mockFoods);
  });

  test('should save food', async () => {
    const mockFood = {
      id: 'food1',
      name: 'Test Food',
      nutrients: [
        { nutrient_id: 'nutrient1', amount: 100 },
        { nutrient_id: 'nutrient2', amount: 5 }
      ]
    };

    const mockSavedFood = {
      ...mockFood,
      updated_at: '2023-01-01T00:00:00.000Z'
    };

    const mockNutrients = [
      { id: 'fn1', food_id: 'food1', nutrient_id: 'nutrient1', amount: 100 }
    ];

    const mockIngredients = [
      { id: 'fi1', food_id: 'food1', ingredient_id: 'ingredient1', name: 'Ingredient 1' }
    ];

    foodDataAccess.update.mockResolvedValue(mockSavedFood);
    foodDataAccess.getNutrients.mockResolvedValue(mockNutrients);
    foodDataAccess.getIngredients.mockResolvedValue(mockIngredients);

    const savedFood = await databaseService.saveFood(mockFood);

    expect(foodDataAccess.update).toHaveBeenCalledWith('food1', mockFood);
    expect(foodDataAccess.saveNutrient).toHaveBeenCalledTimes(2);
    expect(foodDataAccess.getNutrients).toHaveBeenCalledWith('food1');
    expect(foodDataAccess.getIngredients).toHaveBeenCalledWith('food1');
    expect(savedFood).toEqual({
      ...mockSavedFood,
      nutrients: mockNutrients,
      ingredients: mockIngredients
    });
  });

  test('should create new food when id is not provided', async () => {
    const mockFood = {
      name: 'New Food',
      nutrients: [
        { nutrient_id: 'nutrient1', amount: 100 }
      ]
    };

    const mockSavedFood = {
      id: 'mock-uuid',
      name: 'New Food',
      created_at: '2023-01-01T00:00:00.000Z',
      updated_at: '2023-01-01T00:00:00.000Z'
    };

    const mockNutrients = [
      { id: 'fn1', food_id: 'mock-uuid', nutrient_id: 'nutrient1', amount: 100 }
    ];

    const mockIngredients = [];

    foodDataAccess.create.mockResolvedValue(mockSavedFood);
    foodDataAccess.getNutrients.mockResolvedValue(mockNutrients);
    foodDataAccess.getIngredients.mockResolvedValue(mockIngredients);

    const savedFood = await databaseService.saveFood(mockFood);

    expect(foodDataAccess.create).toHaveBeenCalledWith(expect.objectContaining({
      id: expect.any(String),
      name: 'New Food'
    }));
    expect(foodDataAccess.saveNutrient).toHaveBeenCalledTimes(1);
    expect(foodDataAccess.getNutrients).toHaveBeenCalledWith('mock-uuid');
    expect(foodDataAccess.getIngredients).toHaveBeenCalledWith('mock-uuid');
    expect(savedFood).toEqual({
      ...mockSavedFood,
      nutrients: mockNutrients,
      ingredients: mockIngredients
    });
  });

  test('should get consumptions by date', async () => {
    const mockConsumptions = [
      { id: 'consumption1', user_id: 'user1', consumption_date: '2023-01-01', meal_type: 'breakfast' },
      { id: 'consumption2', user_id: 'user1', consumption_date: '2023-01-01', meal_type: 'lunch' }
    ];

    consumptionDataAccess.getByDate.mockResolvedValue(mockConsumptions);

    const consumptions = await databaseService.getConsumptionsByDate('user1', '2023-01-01');

    expect(consumptionDataAccess.getByDate).toHaveBeenCalledWith('user1', '2023-01-01');
    expect(consumptions).toEqual(mockConsumptions);
  });

  test('should save consumption', async () => {
    const mockConsumption = {
      id: 'consumption1',
      user_id: 'user1',
      consumption_date: '2023-01-01',
      meal_type: 'breakfast'
    };

    const mockSavedConsumption = {
      ...mockConsumption,
      updated_at: '2023-01-01T00:00:00.000Z'
    };

    consumptionDataAccess.update.mockResolvedValue(mockSavedConsumption);

    const savedConsumption = await databaseService.saveConsumption(mockConsumption);

    expect(consumptionDataAccess.update).toHaveBeenCalledWith('consumption1', mockConsumption);
    expect(savedConsumption).toEqual(mockSavedConsumption);
  });

  test('should create new consumption when id is not provided', async () => {
    const mockConsumption = {
      user_id: 'user1',
      consumption_date: '2023-01-01',
      meal_type: 'lunch'
    };

    const mockSavedConsumption = {
      id: 'mock-uuid',
      user_id: 'user1',
      consumption_date: '2023-01-01',
      meal_type: 'lunch',
      created_at: '2023-01-01T00:00:00.000Z',
      updated_at: '2023-01-01T00:00:00.000Z'
    };

    consumptionDataAccess.create.mockResolvedValue(mockSavedConsumption);

    const savedConsumption = await databaseService.saveConsumption(mockConsumption);

    expect(consumptionDataAccess.create).toHaveBeenCalledWith(expect.objectContaining({
      id: expect.any(String),
      user_id: 'user1',
      consumption_date: '2023-01-01',
      meal_type: 'lunch'
    }));
    expect(savedConsumption).toEqual(mockSavedConsumption);
  });

  test('should add consumption item', async () => {
    const mockItem = {
      id: 'item1',
      consumption_id: 'consumption1',
      food_id: 'food1',
      quantity: 100,
      unit: 'g'
    };

    consumptionDataAccess.addItem.mockResolvedValue(mockItem);

    const item = await databaseService.addConsumptionItem('consumption1', 'food1', 100, 'g');

    expect(consumptionDataAccess.addItem).toHaveBeenCalledWith('consumption1', 'food1', 100, 'g');
    expect(item).toEqual(mockItem);
  });

  test('should get daily nutrition summary', async () => {
    const mockSummary = {
      date: '2023-01-01',
      calories: 1500,
      protein: 75,
      carbs: 150,
      fat: 50,
      meals: [
        { id: 'consumption1', meal_type: 'breakfast', calories: 500 },
        { id: 'consumption2', meal_type: 'lunch', calories: 1000 }
      ],
      nutrients: {
        'nutrient-calories': { nutrient_id: 'nutrient-calories', name: 'Calories', amount: 1500, unit: 'kcal' },
        'nutrient-protein': { nutrient_id: 'nutrient-protein', name: 'Protein', amount: 75, unit: 'g' }
      }
    };

    consumptionDataAccess.getDailyNutritionSummary.mockResolvedValue(mockSummary);

    const summary = await databaseService.getDailyNutritionSummary('user1', '2023-01-01');

    expect(consumptionDataAccess.getDailyNutritionSummary).toHaveBeenCalledWith('user1', '2023-01-01');
    expect(summary).toEqual(mockSummary);
  });

  test('should get all nutrients', async () => {
    const mockNutrients = [
      { id: 'nutrient1', name: 'Nutrient 1', unit: 'g' },
      { id: 'nutrient2', name: 'Nutrient 2', unit: 'mg' }
    ];

    nutrientDataAccess.getAll.mockResolvedValue(mockNutrients);

    const nutrients = await databaseService.getAllNutrients();

    expect(nutrientDataAccess.getAll).toHaveBeenCalled();
    expect(nutrients).toEqual(mockNutrients);
  });

  test('should get macronutrients', async () => {
    const mockMacros = [
      { id: 'nutrient-calories', name: 'Calories', unit: 'kcal', is_macro: 1 },
      { id: 'nutrient-protein', name: 'Protein', unit: 'g', is_macro: 1 }
    ];

    nutrientDataAccess.getMacronutrients.mockResolvedValue(mockMacros);

    const macros = await databaseService.getMacronutrients();

    expect(nutrientDataAccess.getMacronutrients).toHaveBeenCalled();
    expect(macros).toEqual(mockMacros);
  });

  test('should get meal types', async () => {
    const mockMealTypes = [
      { id: 'meal-breakfast', name: 'Breakfast', display_order: 1 },
      { id: 'meal-lunch', name: 'Lunch', display_order: 2 }
    ];

    mealTypeDataAccess.getAllOrdered.mockResolvedValue(mockMealTypes);

    const mealTypes = await databaseService.getMealTypes();

    expect(mealTypeDataAccess.getAllOrdered).toHaveBeenCalled();
    expect(mealTypes).toEqual(mockMealTypes);
  });
});
