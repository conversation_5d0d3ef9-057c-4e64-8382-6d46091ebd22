import axios from 'axios';

// Open Food Facts API base URL
const API_BASE_URL = 'https://world.openfoodfacts.org/api/v0';

/**
 * Get product information by barcode
 * @param {string} barcode - EAN/UPC barcode
 * @returns {Promise} - Promise with product data
 */
export const getProductByBarcode = async (barcode) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/product/${barcode}.json`);
    
    if (response.data.status === 1) {
      return {
        success: true,
        data: response.data.product
      };
    } else {
      return {
        success: false,
        error: 'Product not found'
      };
    }
  } catch (error) {
    console.error('Error fetching product data:', error);
    return {
      success: false,
      error: error.message || 'Failed to fetch product data'
    };
  }
};

/**
 * Search for products by name
 * @param {string} query - Search query
 * @param {number} page - Page number (default: 1)
 * @param {number} pageSize - Number of results per page (default: 10)
 * @returns {Promise} - Promise with search results
 */
export const searchProducts = async (query, page = 1, pageSize = 10) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/search`, {
      params: {
        search_terms: query,
        page,
        page_size: pageSize,
        json: true
      }
    });
    
    if (response.data.products && response.data.products.length > 0) {
      return {
        success: true,
        data: response.data.products,
        count: response.data.count,
        page: response.data.page,
        pageSize: response.data.page_size
      };
    } else {
      return {
        success: false,
        error: 'No products found'
      };
    }
  } catch (error) {
    console.error('Error searching products:', error);
    return {
      success: false,
      error: error.message || 'Failed to search products'
    };
  }
};
