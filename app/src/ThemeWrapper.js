/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>hler Theme Wrapper
 * Wraps the existing app with our ThemeProvider
 */

import React, { useEffect } from 'react';
import { View, StatusBar, StyleSheet } from 'react-native';
import { ThemeProvider, useTheme } from './theme';

// Import the original app
import OriginalApp from '../test';

// Import dark mode utilities
import { applyDarkModeToAllScreens } from './utils/applyDarkModeToScreens';

// Apply dark mode to all screens
try {
  applyDarkModeToAllScreens();
} catch (error) {
  console.warn('Error applying dark mode to screens:', error);
}

// Dark overlay styles
const styles = StyleSheet.create({
  darkOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    zIndex: 1000,
    pointerEvents: 'none',
  },
});

// Theme wrapper component
const ThemedApp = () => {
  const { colors, isDarkMode } = useTheme();

  // Force dark mode by applying a semi-transparent overlay
  const forceDarkMode = true;

  return (
    <View style={{ flex: 1, backgroundColor: colors.background }}>
      <StatusBar barStyle={colors.statusBar} backgroundColor={colors.headerBackground} />
      <OriginalApp />

      {/* Apply a dark overlay to force dark mode if needed */}
      {forceDarkMode && !isDarkMode && <View style={styles.darkOverlay} />}
    </View>
  );
};

// Main app with theme provider
const ThemeWrapper = () => {
  return (
    <ThemeProvider>
      <ThemedApp />
    </ThemeProvider>
  );
};

export default ThemeWrapper;
