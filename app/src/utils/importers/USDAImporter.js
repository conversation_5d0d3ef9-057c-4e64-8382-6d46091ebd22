/**
 * USDA Food Data Central Importer
 * 
 * This utility imports food data from the USDA Food Data Central database
 * https://fdc.nal.usda.gov/download-datasets.html
 * 
 * The importer supports the following datasets:
 * - Foundation Foods
 * - SR Legacy
 * - Survey Foods (FNDDS)
 * - Branded Foods
 */

import * as FileSystem from 'expo-file-system';
import * as DocumentPicker from 'expo-document-picker';
import * as SQLite from 'expo-sqlite';
import { unzip } from 'react-native-zip-archive';
import { saveFood, saveNutrient, saveIngredient, saveAllergen } from '../../services/databaseService';

// Mapping of USDA nutrient IDs to our internal nutrient types
const NUTRIENT_MAPPING = {
  // Macronutrients
  1003: { name: 'Protein', unit: 'g', type: 'macro' },
  1004: { name: 'Total Fat', unit: 'g', type: 'macro' },
  1005: { name: 'Carbohydrates', unit: 'g', type: 'macro' },
  1008: { name: 'Energy', unit: 'kcal', type: 'macro' },
  
  // Fiber and Sugar
  1079: { name: 'Fiber', unit: 'g', type: 'macro' },
  2000: { name: 'Total Sugars', unit: 'g', type: 'macro' },
  
  // Minerals
  1087: { name: 'Calcium', unit: 'mg', type: 'mineral' },
  1089: { name: 'Iron', unit: 'mg', type: 'mineral' },
  1090: { name: 'Magnesium', unit: 'mg', type: 'mineral' },
  1091: { name: 'Phosphorus', unit: 'mg', type: 'mineral' },
  1092: { name: 'Potassium', unit: 'mg', type: 'mineral' },
  1093: { name: 'Sodium', unit: 'mg', type: 'mineral' },
  1095: { name: 'Zinc', unit: 'mg', type: 'mineral' },
  
  // Vitamins
  1106: { name: 'Vitamin A', unit: 'µg', type: 'vitamin' },
  1162: { name: 'Vitamin C', unit: 'mg', type: 'vitamin' },
  1175: { name: 'Vitamin B6', unit: 'mg', type: 'vitamin' },
  1178: { name: 'Vitamin B12', unit: 'µg', type: 'vitamin' },
  1180: { name: 'Vitamin D', unit: 'µg', type: 'vitamin' },
  1185: { name: 'Vitamin E', unit: 'mg', type: 'vitamin' },
  1190: { name: 'Folate', unit: 'µg', type: 'vitamin' },
};

// Common allergens to look for in ingredients
const COMMON_ALLERGENS = [
  { name: 'Milk', keywords: ['milk', 'dairy', 'lactose', 'whey', 'casein', 'butter', 'cream', 'cheese', 'yogurt'] },
  { name: 'Eggs', keywords: ['egg', 'albumin', 'lysozyme', 'globulin', 'ovomucin', 'ovalbumin', 'ovotransferrin'] },
  { name: 'Fish', keywords: ['fish', 'cod', 'salmon', 'tuna', 'tilapia', 'halibut', 'anchovy', 'bass'] },
  { name: 'Shellfish', keywords: ['shellfish', 'crab', 'lobster', 'shrimp', 'prawn', 'crayfish', 'clam', 'mussel', 'oyster', 'scallop'] },
  { name: 'Tree Nuts', keywords: ['almond', 'hazelnut', 'walnut', 'cashew', 'pecan', 'brazil nut', 'pistachio', 'macadamia'] },
  { name: 'Peanuts', keywords: ['peanut', 'arachis', 'groundnut'] },
  { name: 'Wheat', keywords: ['wheat', 'flour', 'bread', 'bran', 'bulgur', 'durum', 'gluten', 'semolina', 'spelt'] },
  { name: 'Soybeans', keywords: ['soy', 'soya', 'tofu', 'edamame', 'miso', 'tempeh', 'textured vegetable protein', 'tvp'] },
  { name: 'Sesame', keywords: ['sesame', 'tahini', 'sesamol', 'gingelly'] },
  { name: 'Sulfites', keywords: ['sulfite', 'sulphite', 'sulfur dioxide', 'so2', 'e220', 'e221', 'e222', 'e223', 'e224', 'e225', 'e226', 'e227', 'e228'] },
];

/**
 * USDA Food Data Importer class
 */
class USDAImporter {
  constructor() {
    this.tempDir = FileSystem.cacheDirectory + 'usda_import/';
    this.db = null;
    this.importStats = {
      foods: 0,
      nutrients: 0,
      ingredients: 0,
      allergens: 0,
      errors: 0,
    };
  }

  /**
   * Initialize the importer
   */
  async initialize() {
    try {
      // Create temp directory if it doesn't exist
      const dirInfo = await FileSystem.getInfoAsync(this.tempDir);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(this.tempDir, { intermediates: true });
      }
      
      // Clear any existing files
      await this.cleanupTempFiles();
      
      return true;
    } catch (error) {
      console.error('Error initializing USDA importer:', error);
      return false;
    }
  }

  /**
   * Clean up temporary files
   */
  async cleanupTempFiles() {
    try {
      const files = await FileSystem.readDirectoryAsync(this.tempDir);
      for (const file of files) {
        await FileSystem.deleteAsync(this.tempDir + file);
      }
    } catch (error) {
      console.error('Error cleaning up temp files:', error);
    }
  }

  /**
   * Select and download USDA dataset
   * @returns {Promise<string>} Path to the downloaded file
   */
  async selectDataset() {
    try {
      // Let user pick a file
      const result = await DocumentPicker.getDocumentAsync({
        type: ['application/zip', 'application/x-zip-compressed'],
        copyToCacheDirectory: true,
      });
      
      if (result.type === 'success') {
        // Copy the file to our temp directory
        const destPath = this.tempDir + 'usda_dataset.zip';
        await FileSystem.copyAsync({
          from: result.uri,
          to: destPath,
        });
        
        return destPath;
      }
      
      return null;
    } catch (error) {
      console.error('Error selecting USDA dataset:', error);
      return null;
    }
  }

  /**
   * Extract the USDA dataset
   * @param {string} zipPath Path to the zip file
   * @returns {Promise<string>} Path to the extracted directory
   */
  async extractDataset(zipPath) {
    try {
      const extractPath = this.tempDir + 'extracted/';
      
      // Create extract directory if it doesn't exist
      const dirInfo = await FileSystem.getInfoAsync(extractPath);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(extractPath, { intermediates: true });
      }
      
      // Extract the zip file
      await unzip(zipPath, extractPath);
      
      return extractPath;
    } catch (error) {
      console.error('Error extracting USDA dataset:', error);
      return null;
    }
  }

  /**
   * Detect the dataset type based on the files in the extracted directory
   * @param {string} extractPath Path to the extracted directory
   * @returns {Promise<string>} Dataset type ('foundation', 'sr_legacy', 'survey', 'branded', or 'unknown')
   */
  async detectDatasetType(extractPath) {
    try {
      const files = await FileSystem.readDirectoryAsync(extractPath);
      
      // Check for specific files to determine dataset type
      if (files.includes('food.csv') && files.includes('nutrient.csv')) {
        return 'foundation';
      } else if (files.includes('food.csv') && files.includes('food_nutrient.csv')) {
        return 'sr_legacy';
      } else if (files.includes('branded_food.csv')) {
        return 'branded';
      } else if (files.includes('survey_fndds_food.csv')) {
        return 'survey';
      }
      
      return 'unknown';
    } catch (error) {
      console.error('Error detecting dataset type:', error);
      return 'unknown';
    }
  }

  /**
   * Parse a CSV file
   * @param {string} filePath Path to the CSV file
   * @returns {Promise<Array>} Array of objects representing the CSV rows
   */
  async parseCSV(filePath) {
    try {
      const content = await FileSystem.readAsStringAsync(filePath);
      const lines = content.split('\\n');
      
      if (lines.length === 0) {
        return [];
      }
      
      // Parse header
      const header = lines[0].split(',').map(h => h.trim().replace(/^"|"$/g, ''));
      
      // Parse rows
      const rows = [];
      for (let i = 1; i < lines.length; i++) {
        if (!lines[i].trim()) continue;
        
        // Handle quoted values with commas inside
        const values = [];
        let currentValue = '';
        let inQuotes = false;
        
        for (let j = 0; j < lines[i].length; j++) {
          const char = lines[i][j];
          
          if (char === '"') {
            inQuotes = !inQuotes;
          } else if (char === ',' && !inQuotes) {
            values.push(currentValue.replace(/^"|"$/g, ''));
            currentValue = '';
          } else {
            currentValue += char;
          }
        }
        
        // Add the last value
        values.push(currentValue.replace(/^"|"$/g, ''));
        
        // Create object from header and values
        const row = {};
        for (let j = 0; j < header.length; j++) {
          row[header[j]] = values[j] || '';
        }
        
        rows.push(row);
      }
      
      return rows;
    } catch (error) {
      console.error(`Error parsing CSV file ${filePath}:`, error);
      return [];
    }
  }

  /**
   * Import foundation foods dataset
   * @param {string} extractPath Path to the extracted directory
   * @returns {Promise<boolean>} Success status
   */
  async importFoundationFoods(extractPath) {
    try {
      // Parse food data
      const foods = await this.parseCSV(extractPath + 'food.csv');
      const foodNutrients = await this.parseCSV(extractPath + 'food_nutrient.csv');
      const nutrients = await this.parseCSV(extractPath + 'nutrient.csv');
      const ingredients = await this.parseCSV(extractPath + 'input_food.csv');
      
      // Create nutrient lookup
      const nutrientLookup = {};
      for (const nutrient of nutrients) {
        nutrientLookup[nutrient.id] = {
          id: nutrient.id,
          name: nutrient.name,
          unit: nutrient.unit_name,
        };
      }
      
      // Process each food
      for (const food of foods) {
        try {
          // Create food object
          const foodData = {
            name: food.description,
            description: food.description,
            brand_name: '',
            serving_size: 100, // Default to 100g
            serving_unit: 'g',
            barcode: '',
            is_custom: false,
            source: 'USDA Foundation',
            source_id: food.fdc_id,
          };
          
          // Save food to database
          const savedFood = await saveFood(foodData);
          
          // Process nutrients for this food
          const foodNutrientList = foodNutrients.filter(fn => fn.fdc_id === food.fdc_id);
          for (const foodNutrient of foodNutrientList) {
            const nutrientId = foodNutrient.nutrient_id;
            
            // Skip if not in our mapping
            if (!NUTRIENT_MAPPING[nutrientId]) continue;
            
            const nutrientData = {
              food_id: savedFood.id,
              name: NUTRIENT_MAPPING[nutrientId].name,
              amount: parseFloat(foodNutrient.amount) || 0,
              unit: NUTRIENT_MAPPING[nutrientId].unit,
              type: NUTRIENT_MAPPING[nutrientId].type,
            };
            
            // Save nutrient to database
            await saveNutrient(nutrientData);
            this.importStats.nutrients++;
          }
          
          // Process ingredients for this food
          const foodIngredients = ingredients.filter(i => i.fdc_id === food.fdc_id);
          if (foodIngredients.length > 0) {
            for (const ingredient of foodIngredients) {
              const ingredientData = {
                food_id: savedFood.id,
                name: ingredient.name || 'Unknown',
                amount: parseFloat(ingredient.amount) || 0,
                unit: ingredient.unit || '',
              };
              
              // Save ingredient to database
              await saveIngredient(ingredientData);
              this.importStats.ingredients++;
              
              // Check for allergens
              for (const allergen of COMMON_ALLERGENS) {
                const hasAllergen = allergen.keywords.some(keyword => 
                  ingredientData.name.toLowerCase().includes(keyword.toLowerCase())
                );
                
                if (hasAllergen) {
                  const allergenData = {
                    food_id: savedFood.id,
                    name: allergen.name,
                  };
                  
                  // Save allergen to database
                  await saveAllergen(allergenData);
                  this.importStats.allergens++;
                  break; // Only add each allergen once
                }
              }
            }
          }
          
          this.importStats.foods++;
        } catch (error) {
          console.error(`Error importing food ${food.fdc_id}:`, error);
          this.importStats.errors++;
        }
      }
      
      return true;
    } catch (error) {
      console.error('Error importing foundation foods:', error);
      return false;
    }
  }

  /**
   * Import SR Legacy dataset
   * @param {string} extractPath Path to the extracted directory
   * @returns {Promise<boolean>} Success status
   */
  async importSRLegacyFoods(extractPath) {
    try {
      // Parse food data
      const foods = await this.parseCSV(extractPath + 'food.csv');
      const foodNutrients = await this.parseCSV(extractPath + 'food_nutrient.csv');
      const nutrients = await this.parseCSV(extractPath + 'nutrient.csv');
      
      // Create nutrient lookup
      const nutrientLookup = {};
      for (const nutrient of nutrients) {
        nutrientLookup[nutrient.id] = {
          id: nutrient.id,
          name: nutrient.name,
          unit: nutrient.unit_name,
        };
      }
      
      // Process each food
      for (const food of foods) {
        try {
          // Create food object
          const foodData = {
            name: food.description,
            description: food.description,
            brand_name: '',
            serving_size: 100, // Default to 100g
            serving_unit: 'g',
            barcode: '',
            is_custom: false,
            source: 'USDA SR Legacy',
            source_id: food.fdc_id,
          };
          
          // Save food to database
          const savedFood = await saveFood(foodData);
          
          // Process nutrients for this food
          const foodNutrientList = foodNutrients.filter(fn => fn.fdc_id === food.fdc_id);
          for (const foodNutrient of foodNutrientList) {
            const nutrientId = foodNutrient.nutrient_id;
            
            // Skip if not in our mapping
            if (!NUTRIENT_MAPPING[nutrientId]) continue;
            
            const nutrientData = {
              food_id: savedFood.id,
              name: NUTRIENT_MAPPING[nutrientId].name,
              amount: parseFloat(foodNutrient.amount) || 0,
              unit: NUTRIENT_MAPPING[nutrientId].unit,
              type: NUTRIENT_MAPPING[nutrientId].type,
            };
            
            // Save nutrient to database
            await saveNutrient(nutrientData);
            this.importStats.nutrients++;
          }
          
          this.importStats.foods++;
        } catch (error) {
          console.error(`Error importing food ${food.fdc_id}:`, error);
          this.importStats.errors++;
        }
      }
      
      return true;
    } catch (error) {
      console.error('Error importing SR Legacy foods:', error);
      return false;
    }
  }

  /**
   * Import branded foods dataset
   * @param {string} extractPath Path to the extracted directory
   * @returns {Promise<boolean>} Success status
   */
  async importBrandedFoods(extractPath) {
    try {
      // Parse food data
      const foods = await this.parseCSV(extractPath + 'branded_food.csv');
      const foodNutrients = await this.parseCSV(extractPath + 'food_nutrient.csv');
      const nutrients = await this.parseCSV(extractPath + 'nutrient.csv');
      
      // Create nutrient lookup
      const nutrientLookup = {};
      for (const nutrient of nutrients) {
        nutrientLookup[nutrient.id] = {
          id: nutrient.id,
          name: nutrient.name,
          unit: nutrient.unit_name,
        };
      }
      
      // Process each food
      for (const food of foods) {
        try {
          // Create food object
          const foodData = {
            name: food.description || food.brand_name,
            description: food.ingredients || food.description,
            brand_name: food.brand_name || '',
            serving_size: parseFloat(food.serving_size) || 100,
            serving_unit: food.serving_size_unit || 'g',
            barcode: food.gtin_upc || '',
            is_custom: false,
            source: 'USDA Branded',
            source_id: food.fdc_id,
          };
          
          // Save food to database
          const savedFood = await saveFood(foodData);
          
          // Process nutrients for this food
          const foodNutrientList = foodNutrients.filter(fn => fn.fdc_id === food.fdc_id);
          for (const foodNutrient of foodNutrientList) {
            const nutrientId = foodNutrient.nutrient_id;
            
            // Skip if not in our mapping
            if (!NUTRIENT_MAPPING[nutrientId]) continue;
            
            const nutrientData = {
              food_id: savedFood.id,
              name: NUTRIENT_MAPPING[nutrientId].name,
              amount: parseFloat(foodNutrient.amount) || 0,
              unit: NUTRIENT_MAPPING[nutrientId].unit,
              type: NUTRIENT_MAPPING[nutrientId].type,
            };
            
            // Save nutrient to database
            await saveNutrient(nutrientData);
            this.importStats.nutrients++;
          }
          
          // Process ingredients if available
          if (food.ingredients) {
            const ingredientsList = food.ingredients.split(',');
            for (const ingredientName of ingredientsList) {
              if (!ingredientName.trim()) continue;
              
              const ingredientData = {
                food_id: savedFood.id,
                name: ingredientName.trim(),
                amount: 0,
                unit: '',
              };
              
              // Save ingredient to database
              await saveIngredient(ingredientData);
              this.importStats.ingredients++;
              
              // Check for allergens
              for (const allergen of COMMON_ALLERGENS) {
                const hasAllergen = allergen.keywords.some(keyword => 
                  ingredientName.toLowerCase().includes(keyword.toLowerCase())
                );
                
                if (hasAllergen) {
                  const allergenData = {
                    food_id: savedFood.id,
                    name: allergen.name,
                  };
                  
                  // Save allergen to database
                  await saveAllergen(allergenData);
                  this.importStats.allergens++;
                  break; // Only add each allergen once
                }
              }
            }
          }
          
          this.importStats.foods++;
        } catch (error) {
          console.error(`Error importing food ${food.fdc_id}:`, error);
          this.importStats.errors++;
        }
      }
      
      return true;
    } catch (error) {
      console.error('Error importing branded foods:', error);
      return false;
    }
  }

  /**
   * Import survey foods dataset
   * @param {string} extractPath Path to the extracted directory
   * @returns {Promise<boolean>} Success status
   */
  async importSurveyFoods(extractPath) {
    try {
      // Parse food data
      const foods = await this.parseCSV(extractPath + 'survey_fndds_food.csv');
      const foodNutrients = await this.parseCSV(extractPath + 'food_nutrient.csv');
      const nutrients = await this.parseCSV(extractPath + 'nutrient.csv');
      
      // Create nutrient lookup
      const nutrientLookup = {};
      for (const nutrient of nutrients) {
        nutrientLookup[nutrient.id] = {
          id: nutrient.id,
          name: nutrient.name,
          unit: nutrient.unit_name,
        };
      }
      
      // Process each food
      for (const food of foods) {
        try {
          // Create food object
          const foodData = {
            name: food.description,
            description: food.description,
            brand_name: '',
            serving_size: 100, // Default to 100g
            serving_unit: 'g',
            barcode: '',
            is_custom: false,
            source: 'USDA Survey',
            source_id: food.fdc_id,
          };
          
          // Save food to database
          const savedFood = await saveFood(foodData);
          
          // Process nutrients for this food
          const foodNutrientList = foodNutrients.filter(fn => fn.fdc_id === food.fdc_id);
          for (const foodNutrient of foodNutrientList) {
            const nutrientId = foodNutrient.nutrient_id;
            
            // Skip if not in our mapping
            if (!NUTRIENT_MAPPING[nutrientId]) continue;
            
            const nutrientData = {
              food_id: savedFood.id,
              name: NUTRIENT_MAPPING[nutrientId].name,
              amount: parseFloat(foodNutrient.amount) || 0,
              unit: NUTRIENT_MAPPING[nutrientId].unit,
              type: NUTRIENT_MAPPING[nutrientId].type,
            };
            
            // Save nutrient to database
            await saveNutrient(nutrientData);
            this.importStats.nutrients++;
          }
          
          this.importStats.foods++;
        } catch (error) {
          console.error(`Error importing food ${food.fdc_id}:`, error);
          this.importStats.errors++;
        }
      }
      
      return true;
    } catch (error) {
      console.error('Error importing survey foods:', error);
      return false;
    }
  }

  /**
   * Start the import process
   * @returns {Promise<Object>} Import statistics
   */
  async startImport() {
    try {
      // Reset import stats
      this.importStats = {
        foods: 0,
        nutrients: 0,
        ingredients: 0,
        allergens: 0,
        errors: 0,
      };
      
      // Initialize
      const initialized = await this.initialize();
      if (!initialized) {
        throw new Error('Failed to initialize importer');
      }
      
      // Select dataset
      const zipPath = await this.selectDataset();
      if (!zipPath) {
        throw new Error('No dataset selected');
      }
      
      // Extract dataset
      const extractPath = await this.extractDataset(zipPath);
      if (!extractPath) {
        throw new Error('Failed to extract dataset');
      }
      
      // Detect dataset type
      const datasetType = await this.detectDatasetType(extractPath);
      if (datasetType === 'unknown') {
        throw new Error('Unknown dataset type');
      }
      
      // Import based on dataset type
      let success = false;
      switch (datasetType) {
        case 'foundation':
          success = await this.importFoundationFoods(extractPath);
          break;
        case 'sr_legacy':
          success = await this.importSRLegacyFoods(extractPath);
          break;
        case 'branded':
          success = await this.importBrandedFoods(extractPath);
          break;
        case 'survey':
          success = await this.importSurveyFoods(extractPath);
          break;
      }
      
      // Clean up
      await this.cleanupTempFiles();
      
      return {
        success,
        datasetType,
        stats: this.importStats,
      };
    } catch (error) {
      console.error('Error importing USDA dataset:', error);
      return {
        success: false,
        error: error.message,
        stats: this.importStats,
      };
    }
  }
}

export default USDAImporter;
