/**
 * FoodB Database Importer
 * 
 * This utility imports food data from the FoodB database
 * https://foodb.ca/public/system/downloads/foodb_2020_04_07_json.zip
 * 
 * The importer supports the JSON format dataset from FoodB
 */

import * as FileSystem from 'expo-file-system';
import * as DocumentPicker from 'expo-document-picker';
import { unzip } from 'react-native-zip-archive';
import { saveFood, saveNutrient, saveIngredient, saveAllergen } from '../../services/databaseService';

// Common allergens to look for in ingredients
const COMMON_ALLERGENS = [
  { name: 'Milk', keywords: ['milk', 'dairy', 'lactose', 'whey', 'casein', 'butter', 'cream', 'cheese', 'yogurt'] },
  { name: 'Eggs', keywords: ['egg', 'albumin', 'lysozyme', 'globulin', 'ovomucin', 'ovalbumin', 'ovotransferrin'] },
  { name: 'Fish', keywords: ['fish', 'cod', 'salmon', 'tuna', 'tilapia', 'halibut', 'anchovy', 'bass'] },
  { name: 'Shellfish', keywords: ['shellfish', 'crab', 'lobster', 'shrimp', 'prawn', 'crayfish', 'clam', 'mussel', 'oyster', 'scallop'] },
  { name: 'Tree Nuts', keywords: ['almond', 'hazelnut', 'walnut', 'cashew', 'pecan', 'brazil nut', 'pistachio', 'macadamia'] },
  { name: 'Peanuts', keywords: ['peanut', 'arachis', 'groundnut'] },
  { name: 'Wheat', keywords: ['wheat', 'flour', 'bread', 'bran', 'bulgur', 'durum', 'gluten', 'semolina', 'spelt'] },
  { name: 'Soybeans', keywords: ['soy', 'soya', 'tofu', 'edamame', 'miso', 'tempeh', 'textured vegetable protein', 'tvp'] },
  { name: 'Sesame', keywords: ['sesame', 'tahini', 'sesamol', 'gingelly'] },
  { name: 'Sulfites', keywords: ['sulfite', 'sulphite', 'sulfur dioxide', 'so2', 'e220', 'e221', 'e222', 'e223', 'e224', 'e225', 'e226', 'e227', 'e228'] },
];

// Mapping of FoodB nutrient names to our internal nutrient types
const NUTRIENT_MAPPING = {
  // Macronutrients
  'Protein': { name: 'Protein', unit: 'g', type: 'macro' },
  'Total lipid (fat)': { name: 'Total Fat', unit: 'g', type: 'macro' },
  'Carbohydrate, by difference': { name: 'Carbohydrates', unit: 'g', type: 'macro' },
  'Energy': { name: 'Energy', unit: 'kcal', type: 'macro' },
  
  // Fiber and Sugar
  'Fiber, total dietary': { name: 'Fiber', unit: 'g', type: 'macro' },
  'Sugars, total': { name: 'Total Sugars', unit: 'g', type: 'macro' },
  
  // Minerals
  'Calcium, Ca': { name: 'Calcium', unit: 'mg', type: 'mineral' },
  'Iron, Fe': { name: 'Iron', unit: 'mg', type: 'mineral' },
  'Magnesium, Mg': { name: 'Magnesium', unit: 'mg', type: 'mineral' },
  'Phosphorus, P': { name: 'Phosphorus', unit: 'mg', type: 'mineral' },
  'Potassium, K': { name: 'Potassium', unit: 'mg', type: 'mineral' },
  'Sodium, Na': { name: 'Sodium', unit: 'mg', type: 'mineral' },
  'Zinc, Zn': { name: 'Zinc', unit: 'mg', type: 'mineral' },
  
  // Vitamins
  'Vitamin A, RAE': { name: 'Vitamin A', unit: 'µg', type: 'vitamin' },
  'Vitamin C, total ascorbic acid': { name: 'Vitamin C', unit: 'mg', type: 'vitamin' },
  'Vitamin B-6': { name: 'Vitamin B6', unit: 'mg', type: 'vitamin' },
  'Vitamin B-12': { name: 'Vitamin B12', unit: 'µg', type: 'vitamin' },
  'Vitamin D (D2 + D3)': { name: 'Vitamin D', unit: 'µg', type: 'vitamin' },
  'Vitamin E (alpha-tocopherol)': { name: 'Vitamin E', unit: 'mg', type: 'vitamin' },
  'Folate, total': { name: 'Folate', unit: 'µg', type: 'vitamin' },
};

/**
 * FoodB Data Importer class
 */
class FoodBImporter {
  constructor() {
    this.tempDir = FileSystem.cacheDirectory + 'foodb_import/';
    this.importStats = {
      foods: 0,
      nutrients: 0,
      ingredients: 0,
      allergens: 0,
      errors: 0,
    };
  }

  /**
   * Initialize the importer
   */
  async initialize() {
    try {
      // Create temp directory if it doesn't exist
      const dirInfo = await FileSystem.getInfoAsync(this.tempDir);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(this.tempDir, { intermediates: true });
      }
      
      // Clear any existing files
      await this.cleanupTempFiles();
      
      return true;
    } catch (error) {
      console.error('Error initializing FoodB importer:', error);
      return false;
    }
  }

  /**
   * Clean up temporary files
   */
  async cleanupTempFiles() {
    try {
      const files = await FileSystem.readDirectoryAsync(this.tempDir);
      for (const file of files) {
        await FileSystem.deleteAsync(this.tempDir + file);
      }
    } catch (error) {
      console.error('Error cleaning up temp files:', error);
    }
  }

  /**
   * Select and download FoodB dataset
   * @returns {Promise<string>} Path to the downloaded file
   */
  async selectDataset() {
    try {
      // Let user pick a file
      const result = await DocumentPicker.getDocumentAsync({
        type: ['application/zip', 'application/x-zip-compressed'],
        copyToCacheDirectory: true,
      });
      
      if (result.type === 'success') {
        // Copy the file to our temp directory
        const destPath = this.tempDir + 'foodb_dataset.zip';
        await FileSystem.copyAsync({
          from: result.uri,
          to: destPath,
        });
        
        return destPath;
      }
      
      return null;
    } catch (error) {
      console.error('Error selecting FoodB dataset:', error);
      return null;
    }
  }

  /**
   * Extract the FoodB dataset
   * @param {string} zipPath Path to the zip file
   * @returns {Promise<string>} Path to the extracted directory
   */
  async extractDataset(zipPath) {
    try {
      const extractPath = this.tempDir + 'extracted/';
      
      // Create extract directory if it doesn't exist
      const dirInfo = await FileSystem.getInfoAsync(extractPath);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(extractPath, { intermediates: true });
      }
      
      // Extract the zip file
      await unzip(zipPath, extractPath);
      
      return extractPath;
    } catch (error) {
      console.error('Error extracting FoodB dataset:', error);
      return null;
    }
  }

  /**
   * Validate the dataset to ensure it's a FoodB dataset
   * @param {string} extractPath Path to the extracted directory
   * @returns {Promise<boolean>} Whether the dataset is valid
   */
  async validateDataset(extractPath) {
    try {
      const files = await FileSystem.readDirectoryAsync(extractPath);
      
      // Check for required JSON files
      const requiredFiles = ['Food.json', 'Nutrient.json', 'Compound.json'];
      for (const file of requiredFiles) {
        if (!files.includes(file)) {
          console.error(`Missing required file: ${file}`);
          return false;
        }
      }
      
      return true;
    } catch (error) {
      console.error('Error validating FoodB dataset:', error);
      return false;
    }
  }

  /**
   * Parse a JSON file
   * @param {string} filePath Path to the JSON file
   * @returns {Promise<Array|Object>} Parsed JSON data
   */
  async parseJSON(filePath) {
    try {
      const content = await FileSystem.readAsStringAsync(filePath);
      return JSON.parse(content);
    } catch (error) {
      console.error(`Error parsing JSON file ${filePath}:`, error);
      return null;
    }
  }

  /**
   * Import foods from the FoodB dataset
   * @param {string} extractPath Path to the extracted directory
   * @returns {Promise<boolean>} Success status
   */
  async importFoods(extractPath) {
    try {
      // Parse food data
      const foodsData = await this.parseJSON(extractPath + 'Food.json');
      const nutrientsData = await this.parseJSON(extractPath + 'Nutrient.json');
      const compoundsData = await this.parseJSON(extractPath + 'Compound.json');
      
      if (!foodsData || !nutrientsData || !compoundsData) {
        throw new Error('Failed to parse required JSON files');
      }
      
      // Create lookup maps
      const nutrientMap = {};
      nutrientsData.forEach(nutrient => {
        nutrientMap[nutrient.id] = nutrient;
      });
      
      const compoundMap = {};
      compoundsData.forEach(compound => {
        compoundMap[compound.id] = compound;
      });
      
      // Process each food
      for (const food of foodsData) {
        try {
          // Skip foods without a name
          if (!food.name) continue;
          
          // Create food object
          const foodData = {
            name: food.name,
            description: food.description || food.name,
            brand_name: '',
            serving_size: 100, // Default to 100g
            serving_unit: 'g',
            barcode: '',
            is_custom: false,
            source: 'FoodB',
            source_id: food.id.toString(),
          };
          
          // Save food to database
          const savedFood = await saveFood(foodData);
          
          // Process nutrients for this food
          if (food.nutrients && Array.isArray(food.nutrients)) {
            for (const foodNutrient of food.nutrients) {
              const nutrient = nutrientMap[foodNutrient.nutrient_id];
              if (!nutrient) continue;
              
              // Skip if not in our mapping
              const mappedNutrient = NUTRIENT_MAPPING[nutrient.name];
              if (!mappedNutrient) continue;
              
              const nutrientData = {
                food_id: savedFood.id,
                name: mappedNutrient.name,
                amount: parseFloat(foodNutrient.amount) || 0,
                unit: mappedNutrient.unit,
                type: mappedNutrient.type,
              };
              
              // Save nutrient to database
              await saveNutrient(nutrientData);
              this.importStats.nutrients++;
            }
          }
          
          // Process compounds as ingredients
          if (food.compounds && Array.isArray(food.compounds)) {
            for (const foodCompound of food.compounds) {
              const compound = compoundMap[foodCompound.compound_id];
              if (!compound || !compound.name) continue;
              
              const ingredientData = {
                food_id: savedFood.id,
                name: compound.name,
                amount: parseFloat(foodCompound.amount) || 0,
                unit: foodCompound.unit || 'mg',
              };
              
              // Save ingredient to database
              await saveIngredient(ingredientData);
              this.importStats.ingredients++;
              
              // Check for allergens
              for (const allergen of COMMON_ALLERGENS) {
                const hasAllergen = allergen.keywords.some(keyword => 
                  compound.name.toLowerCase().includes(keyword.toLowerCase())
                );
                
                if (hasAllergen) {
                  const allergenData = {
                    food_id: savedFood.id,
                    name: allergen.name,
                  };
                  
                  // Save allergen to database
                  await saveAllergen(allergenData);
                  this.importStats.allergens++;
                  break; // Only add each allergen once
                }
              }
            }
          }
          
          this.importStats.foods++;
        } catch (error) {
          console.error(`Error importing food ${food.id}:`, error);
          this.importStats.errors++;
        }
      }
      
      return true;
    } catch (error) {
      console.error('Error importing FoodB foods:', error);
      return false;
    }
  }

  /**
   * Start the import process
   * @returns {Promise<Object>} Import statistics
   */
  async startImport() {
    try {
      // Reset import stats
      this.importStats = {
        foods: 0,
        nutrients: 0,
        ingredients: 0,
        allergens: 0,
        errors: 0,
      };
      
      // Initialize
      const initialized = await this.initialize();
      if (!initialized) {
        throw new Error('Failed to initialize importer');
      }
      
      // Select dataset
      const zipPath = await this.selectDataset();
      if (!zipPath) {
        throw new Error('No dataset selected');
      }
      
      // Extract dataset
      const extractPath = await this.extractDataset(zipPath);
      if (!extractPath) {
        throw new Error('Failed to extract dataset');
      }
      
      // Validate dataset
      const isValid = await this.validateDataset(extractPath);
      if (!isValid) {
        throw new Error('Invalid FoodB dataset');
      }
      
      // Import foods
      const success = await this.importFoods(extractPath);
      
      // Clean up
      await this.cleanupTempFiles();
      
      return {
        success,
        stats: this.importStats,
      };
    } catch (error) {
      console.error('Error importing FoodB dataset:', error);
      return {
        success: false,
        error: error.message,
        stats: this.importStats,
      };
    }
  }
}

export default FoodBImporter;
