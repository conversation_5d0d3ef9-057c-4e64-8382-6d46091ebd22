/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>hler Dark Mode Utility
 * Helper functions to apply dark mode to existing screens
 */

// Dark mode colors
const darkColors = {
  background: '#121212',
  surface: '#1E1E1E',
  primary: '#4CAF50',
  accent: '#03DAC6',
  error: '#CF6679',
  text: '#FFFFFF',
  textSecondary: '#B0B0B0',
  border: '#2C2C2C',
  cardBackground: '#2C2C2C',
  headerBackground: '#1A1A1A',
  buttonBackground: '#333333',
  inputBackground: '#333333',
  statusBar: 'light-content',
  icon: '#FFFFFF',
  summaryBackground: '#262626',
  profileButton: '#388E3C',
  manualEntryHeader: '#333333',
  manualEntrySubheader: '#FF9800',
  userProfileHeader: '#121212',
  settingsItemBackground: '#2C2C2C',
};

// Apply dark mode to a style object
export const applyDarkMode = (styles) => {
  const darkModeStyles = { ...styles };
  
  // Update background colors
  if (darkModeStyles.container) {
    darkModeStyles.container = {
      ...darkModeStyles.container,
      backgroundColor: darkColors.background,
    };
  }
  
  // Update text colors
  Object.keys(darkModeStyles).forEach(key => {
    if (darkModeStyles[key] && typeof darkModeStyles[key] === 'object') {
      // Check if the style has color property
      if (darkModeStyles[key].color) {
        darkModeStyles[key] = {
          ...darkModeStyles[key],
          color: darkColors.text,
        };
      }
      
      // Check if the style has backgroundColor property
      if (darkModeStyles[key].backgroundColor) {
        // Header backgrounds
        if (key.includes('header')) {
          darkModeStyles[key] = {
            ...darkModeStyles[key],
            backgroundColor: darkColors.headerBackground,
          };
        } 
        // Card backgrounds
        else if (key.includes('card') || key.includes('section') || key.includes('item')) {
          darkModeStyles[key] = {
            ...darkModeStyles[key],
            backgroundColor: darkColors.cardBackground,
          };
        }
        // Input backgrounds
        else if (key.includes('input')) {
          darkModeStyles[key] = {
            ...darkModeStyles[key],
            backgroundColor: darkColors.inputBackground,
            borderColor: darkColors.border,
          };
        }
        // Button backgrounds
        else if (key.includes('button')) {
          // Primary buttons keep their color
          if (!key.includes('primary') && !key.includes('submit')) {
            darkModeStyles[key] = {
              ...darkModeStyles[key],
              backgroundColor: darkColors.buttonBackground,
            };
          }
        }
        // Other backgrounds
        else {
          darkModeStyles[key] = {
            ...darkModeStyles[key],
            backgroundColor: darkColors.background,
          };
        }
      }
      
      // Update border colors
      if (darkModeStyles[key].borderColor) {
        darkModeStyles[key] = {
          ...darkModeStyles[key],
          borderColor: darkColors.border,
        };
      }
    }
  });
  
  return darkModeStyles;
};

// Apply dark mode to a component's props
export const applyDarkModeProps = (props) => {
  const darkModeProps = { ...props };
  
  // Update StatusBar props
  if (darkModeProps.barStyle) {
    darkModeProps.barStyle = darkColors.statusBar;
  }
  
  return darkModeProps;
};
