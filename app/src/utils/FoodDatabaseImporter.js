/**
 * Food Database Importer for Znü<PERSON>Zähler
 * 
 * This utility imports food data from pre-built SQLite databases
 * into the app's main database.
 */

import * as SQLite from 'expo-sqlite';
import * as FileSystem from 'expo-file-system';
import { Asset } from 'expo-asset';
import dbManager from '../database/DatabaseManager';

/**
 * Food Database Importer class
 */
class FoodDatabaseImporter {
  /**
   * Import food data from a pre-built SQLite database
   * @param {string} databaseName - Name of the database file (without extension)
   * @param {Function} progressCallback - Callback function for progress updates
   * @returns {Promise<Object>} - Import statistics
   */
  async importFoodDatabase(databaseName, progressCallback = null) {
    try {
      // Statistics object
      const stats = {
        foods: 0,
        nutrients: 0,
        ingredients: 0,
        allergens: 0,
        errors: 0
      };
      
      // Update progress
      if (progressCallback) {
        progressCallback({
          status: 'preparing',
          message: 'Preparing to import food database...',
          progress: 0.1
        });
      }
      
      // Copy the database file from assets to a temporary location
      const dbAsset = Asset.fromModule(require(`../assets/databases/${databaseName}.sqlite`));
      await dbAsset.downloadAsync();
      
      const tempDbPath = `${FileSystem.cacheDirectory}${databaseName}_temp.db`;
      await FileSystem.copyAsync({
        from: dbAsset.localUri,
        to: tempDbPath
      });
      
      // Open the source database
      const sourceDb = SQLite.openDatabase(`${databaseName}_temp.db`);
      
      // Update progress
      if (progressCallback) {
        progressCallback({
          status: 'counting',
          message: 'Counting records to import...',
          progress: 0.2
        });
      }
      
      // Count the number of foods to import
      const foodCount = await this._executeQuery(sourceDb, 'SELECT COUNT(*) as count FROM foods');
      const totalFoods = foodCount.rows.item(0).count;
      
      // Update progress
      if (progressCallback) {
        progressCallback({
          status: 'importing',
          message: `Importing ${totalFoods} foods...`,
          progress: 0.3,
          totalFoods
        });
      }
      
      // Get all foods from the source database
      const foods = await this._executeQuery(sourceDb, 'SELECT * FROM foods');
      
      // Import each food
      for (let i = 0; i < foods.rows.length; i++) {
        const food = foods.rows.item(i);
        
        try {
          // Create food object
          const foodData = {
            name: food.name,
            description: food.description || food.name,
            brand_name: food.brand_name || '',
            serving_size: food.serving_size || 100,
            serving_unit: food.serving_unit || 'g',
            barcode: food.barcode || '',
            is_custom: 0,
            source: food.source || '',
            source_id: food.source_id || ''
          };
          
          // Save food to database
          const result = await dbManager.executeQuery(
            `INSERT INTO Food (name, description, brand_name, serving_size, serving_unit, barcode, is_custom, source, source_id)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              foodData.name,
              foodData.description,
              foodData.brand_name,
              foodData.serving_size,
              foodData.serving_unit,
              foodData.barcode,
              foodData.is_custom,
              foodData.source,
              foodData.source_id
            ]
          );
          
          const foodId = result.insertId;
          
          // Get nutrients for this food
          const nutrients = await this._executeQuery(
            sourceDb,
            'SELECT * FROM nutrients WHERE food_id = ?',
            [food.id]
          );
          
          // Import nutrients
          for (let j = 0; j < nutrients.rows.length; j++) {
            const nutrient = nutrients.rows.item(j);
            
            await dbManager.executeQuery(
              `INSERT INTO Nutrient (food_id, name, amount, unit, type)
               VALUES (?, ?, ?, ?, ?)`,
              [
                foodId,
                nutrient.name,
                nutrient.amount,
                nutrient.unit,
                nutrient.type
              ]
            );
            
            stats.nutrients++;
          }
          
          // Get ingredients for this food
          const ingredients = await this._executeQuery(
            sourceDb,
            'SELECT * FROM ingredients WHERE food_id = ?',
            [food.id]
          );
          
          // Import ingredients
          for (let j = 0; j < ingredients.rows.length; j++) {
            const ingredient = ingredients.rows.item(j);
            
            await dbManager.executeQuery(
              `INSERT INTO Ingredient (food_id, name, amount, unit)
               VALUES (?, ?, ?, ?)`,
              [
                foodId,
                ingredient.name,
                ingredient.amount || 0,
                ingredient.unit || ''
              ]
            );
            
            stats.ingredients++;
          }
          
          // Get allergens for this food
          const allergens = await this._executeQuery(
            sourceDb,
            'SELECT * FROM allergens WHERE food_id = ?',
            [food.id]
          );
          
          // Import allergens
          for (let j = 0; j < allergens.rows.length; j++) {
            const allergen = allergens.rows.item(j);
            
            await dbManager.executeQuery(
              `INSERT INTO Allergen (food_id, name)
               VALUES (?, ?)`,
              [
                foodId,
                allergen.name
              ]
            );
            
            stats.allergens++;
          }
          
          stats.foods++;
          
          // Update progress
          if (progressCallback) {
            progressCallback({
              status: 'importing',
              message: `Imported ${stats.foods} of ${totalFoods} foods...`,
              progress: 0.3 + (0.6 * (stats.foods / totalFoods)),
              importedFoods: stats.foods,
              totalFoods
            });
          }
        } catch (error) {
          console.error(`Error importing food ${food.id}:`, error);
          stats.errors++;
        }
      }
      
      // Close the source database
      sourceDb._db.close();
      
      // Delete the temporary database file
      await FileSystem.deleteAsync(tempDbPath, { idempotent: true });
      
      // Update progress
      if (progressCallback) {
        progressCallback({
          status: 'complete',
          message: 'Import complete!',
          progress: 1,
          stats
        });
      }
      
      return stats;
    } catch (error) {
      console.error('Error importing food database:', error);
      
      // Update progress with error
      if (progressCallback) {
        progressCallback({
          status: 'error',
          message: `Error: ${error.message}`,
          progress: 0,
          error
        });
      }
      
      throw error;
    }
  }
  
  /**
   * Execute a SQL query on a database
   * @param {Object} db - SQLite database
   * @param {string} sql - SQL query
   * @param {Array} params - Query parameters
   * @returns {Promise<Object>} - Query result
   * @private
   */
  _executeQuery(db, sql, params = []) {
    return new Promise((resolve, reject) => {
      db.transaction(tx => {
        tx.executeSql(
          sql,
          params,
          (_, result) => {
            resolve(result);
          },
          (_, error) => {
            console.error('SQL Error:', error);
            reject(error);
            return false;
          }
        );
      });
    });
  }
}

export default new FoodDatabaseImporter();
