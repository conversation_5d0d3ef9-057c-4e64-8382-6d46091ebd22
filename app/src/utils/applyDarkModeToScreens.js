/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>hler Apply Dark Mode to Screens
 * Script to apply dark mode to all screens in the app
 */

import { StyleSheet } from 'react-native';
import { applyDarkMode } from './applyDarkMode';

// Function to apply dark mode to a screen's styles
export const applyDarkModeToScreen = (originalStyles) => {
  // Create a new StyleSheet with dark mode applied
  const darkModeStyles = StyleSheet.create(applyDarkMode(originalStyles));
  
  return darkModeStyles;
};

// Function to override a component's render method to use dark mode
export const withDarkMode = (Component) => {
  // Save the original render method
  const originalRender = Component.prototype.render;
  
  // Override the render method
  Component.prototype.render = function() {
    // Apply dark mode to the component's styles
    if (this.styles) {
      this.styles = applyDarkModeToScreen(this.styles);
    }
    
    // Call the original render method
    return originalRender.call(this);
  };
  
  return Component;
};

// Apply dark mode to all screens
export const applyDarkModeToAllScreens = () => {
  // Import all screen components
  const screens = [
    require('../screens/HomeScreen').default,
    require('../screens/ManualEntryScreen').default,
    require('../screens/ProfileScreen').default,
    require('../screens/BarcodeScreen').default,
    require('../screens/FoodDetailsScreen').default,
    require('../screens/ScanScreen').default,
    require('../screens/StatisticsScreen').default,
  ];
  
  // Apply dark mode to each screen
  screens.forEach(screen => {
    withDarkMode(screen);
  });
};
