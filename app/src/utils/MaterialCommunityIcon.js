/**
 * Custom Material Community Icon for React Native Paper
 * This is a workaround for the missing @react-native-vector-icons/material-design-icons package
 */

import React from 'react';
import { Text } from 'react-native';

/**
 * Custom Material Community Icon Component
 * @param {Object} props - Component props
 * @returns {JSX.Element} - Icon component
 */
const MaterialCommunityIcon = ({ name, color, size, ...rest }) => {
  // This is a placeholder icon
  return (
    <Text
      style={{
        color,
        fontSize: size,
        fontWeight: 'bold',
        textAlign: 'center',
        width: size,
        height: size,
      }}
      {...rest}
    >
      {name.charAt(0).toUpperCase()}
    </Text>
  );
};

export default MaterialCommunityIcon;
