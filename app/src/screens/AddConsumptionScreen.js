/**
 * Add Consumption Screen for Znü<PERSON>Zähler
 * Allows users to add or edit food consumption
 */

import React, { useState, useEffect } from 'react';
import { StyleSheet, View, BackHandler } from 'react-native';
import { useTheme } from '../theme/ThemeProvider';
import { Appbar, FAB, Portal, Snackbar } from 'react-native-paper';
import ConsumptionEntry from '../components/ConsumptionEntry';
import FoodList from '../components/FoodList';

/**
 * Add Consumption Screen Component
 * @param {Object} props - Component props
 * @param {Object} props.route - Route object
 * @param {Object} props.navigation - Navigation object
 * @returns {JSX.Element} - Add consumption screen component
 */
const AddConsumptionScreen = ({ route, navigation }) => {
  const { theme } = useTheme();
  const { consumption, date } = route.params || {};
  const [showFoodList, setShowFoodList] = useState(false);
  const [selectedFoods, setSelectedFoods] = useState([]);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // Handle back button press
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      if (showFoodList) {
        setShowFoodList(false);
        return true;
      }
      return false;
    });

    return () => backHandler.remove();
  }, [showFoodList]);

  // Handle save consumption
  const handleSaveConsumption = (savedConsumption) => {
    setSnackbarMessage('Consumption saved successfully');
    setSnackbarVisible(true);

    // Navigate back after a short delay
    setTimeout(() => {
      navigation.goBack();
    }, 1500);
  };

  // Handle cancel
  const handleCancel = () => {
    navigation.goBack();
  };

  // Handle select food
  const handleSelectFood = (food) => {
    // Check if food is already selected
    const existingIndex = selectedFoods.findIndex(f => f.id === food.id);

    if (existingIndex >= 0) {
      // Update quantity if already selected
      const updatedFoods = [...selectedFoods];
      updatedFoods[existingIndex] = {
        ...updatedFoods[existingIndex],
        quantity: updatedFoods[existingIndex].quantity + 100
      };
      setSelectedFoods(updatedFoods);
    } else {
      // Add new food with default quantity
      setSelectedFoods([...selectedFoods, {
        ...food,
        quantity: 100,
        unit: food.serving_unit || 'g'
      }]);
    }

    // Show snackbar
    setSnackbarMessage(`${food.name} added to meal`);
    setSnackbarVisible(true);
  };

  // Handle remove food
  const handleRemoveFood = (foodId) => {
    setSelectedFoods(selectedFoods.filter(food => food.id !== foodId));
  };

  // Handle quantity change
  const handleQuantityChange = (foodId, quantity) => {
    setSelectedFoods(selectedFoods.map(food =>
      food.id === foodId ? { ...food, quantity } : food
    ));
  };

  // Handle continue to entry
  const handleContinueToEntry = () => {
    setShowFoodList(false);
  };

  // Render app bar
  const renderAppBar = () => (
    <Appbar.Header>
      <Appbar.BackAction onPress={() => {
        if (showFoodList) {
          setShowFoodList(false);
        } else {
          navigation.goBack();
        }
      }} />
      <Appbar.Content title={showFoodList ? "Select Foods" : "Log Consumption"} />
      {showFoodList && selectedFoods.length > 0 && (
        <Appbar.Action
          icon="check"
          onPress={handleContinueToEntry}
        />
      )}
    </Appbar.Header>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {renderAppBar()}

      {showFoodList ? (
        <View style={styles.container}>
          <FoodList
            onSelectFood={handleSelectFood}
            onAddFood={() => navigation.navigate('FoodDetail', { isNew: true })}
          />

          {selectedFoods.length > 0 && (
            <FAB
              icon="check"
              label={`Continue with ${selectedFoods.length} food${selectedFoods.length > 1 ? 's' : ''}`}
              style={[styles.fab, { backgroundColor: theme.colors.primary }]}
              onPress={handleContinueToEntry}
              extended
            />
          )}
        </View>
      ) : (
        <ConsumptionEntry
          consumption={consumption}
          onSave={handleSaveConsumption}
          onCancel={handleCancel}
          onSelectFood={() => setShowFoodList(true)}
          selectedFoods={selectedFoods}
          onRemoveFood={handleRemoveFood}
          onQuantityChange={handleQuantityChange}
          date={date}
        />
      )}

      <Portal>
        <Snackbar
          visible={snackbarVisible}
          onDismiss={() => setSnackbarVisible(false)}
          duration={2000}
          action={{
            label: 'OK',
            onPress: () => setSnackbarVisible(false),
          }}
        >
          {snackbarMessage}
        </Snackbar>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});

export default AddConsumptionScreen;
