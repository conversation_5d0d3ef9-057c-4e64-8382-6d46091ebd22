/**
 * Data Access Layer for Znü<PERSON>Zähler
 * Provides a higher-level API for database operations
 */

import dbManager from './DatabaseManager';
import { v4 as uuidv4 } from 'uuid';

/**
 * Base data access class with common CRUD operations
 */
class BaseDataAccess {
  /**
   * Create a new BaseDataAccess instance
   * @param {string} tableName - Database table name
   * @param {string} idField - Primary key field name (default: 'id')
   */
  constructor(tableName, idField = 'id') {
    this.tableName = tableName;
    this.idField = idField;
  }

  /**
   * Get all active records
   * @param {Object} options - Query options
   * @param {number} options.limit - Maximum number of records to return
   * @param {number} options.offset - Number of records to skip
   * @param {string} options.orderBy - Field to order by
   * @param {string} options.order - Order direction ('ASC' or 'DESC')
   * @returns {Promise<Array>} - Array of records
   */
  async getAll(options = {}) {
    const { limit, offset, orderBy, order } = options;

    let query = `SELECT * FROM ${this.tableName} WHERE is_active = 1 AND deleted_at IS NULL`;

    if (orderBy) {
      query += ` ORDER BY ${orderBy} ${order || 'ASC'}`;
    }

    if (limit) {
      query += ` LIMIT ${limit}`;

      if (offset) {
        query += ` OFFSET ${offset}`;
      }
    }

    const result = await dbManager.executeQuery(query);

    const items = [];
    for (let i = 0; i < result.rows.length; i++) {
      items.push(result.rows.item(i));
    }

    return items;
  }

  /**
   * Get a record by ID
   * @param {string} id - Record ID
   * @returns {Promise<Object|null>} - Record or null if not found
   */
  async getById(id) {
    const result = await dbManager.executeQuery(
      `SELECT * FROM ${this.tableName} WHERE ${this.idField} = ? AND is_active = 1 AND deleted_at IS NULL`,
      [id]
    );

    return result.rows.length > 0 ? result.rows.item(0) : null;
  }

  /**
   * Create a new record
   * @param {Object} data - Record data
   * @returns {Promise<Object>} - Created record
   */
  async create(data) {
    // Generate ID if not provided
    const recordData = { ...data };
    if (!recordData[this.idField]) {
      recordData[this.idField] = uuidv4();
    }

    // Add timestamps if not provided
    const now = new Date().toISOString();
    if (!recordData.created_at) {
      recordData.created_at = now;
    }
    if (!recordData.updated_at) {
      recordData.updated_at = now;
    }

    // Set is_active to 1 if not provided
    if (recordData.is_active === undefined) {
      recordData.is_active = 1;
    }

    const columns = Object.keys(recordData).join(', ');
    const placeholders = Object.keys(recordData).map(() => '?').join(', ');
    const values = Object.values(recordData);

    await dbManager.executeQuery(
      `INSERT INTO ${this.tableName} (${columns}) VALUES (${placeholders})`,
      values
    );

    return this.getById(recordData[this.idField]);
  }

  /**
   * Update a record
   * @param {string} id - Record ID
   * @param {Object} data - Record data
   * @returns {Promise<Object>} - Updated record
   */
  async update(id, data) {
    // Add updated_at timestamp if not provided
    const recordData = { ...data };
    if (!recordData.updated_at) {
      recordData.updated_at = new Date().toISOString();
    }

    // Remove id field from data if present
    delete recordData[this.idField];

    const setClause = Object.keys(recordData).map(key => `${key} = ?`).join(', ');
    const values = [...Object.values(recordData), id];

    await dbManager.executeQuery(
      `UPDATE ${this.tableName} SET ${setClause} WHERE ${this.idField} = ?`,
      values
    );

    return this.getById(id);
  }

  /**
   * Soft delete a record
   * @param {string} id - Record ID
   * @returns {Promise<boolean>} - Success flag
   */
  async delete(id) {
    const result = await dbManager.executeQuery(
      `UPDATE ${this.tableName} SET is_active = 0, deleted_at = datetime('now') WHERE ${this.idField} = ?`,
      [id]
    );

    return result.rowsAffected > 0;
  }

  /**
   * Find records by field value
   * @param {string} field - Field name
   * @param {*} value - Field value
   * @param {Object} options - Query options
   * @returns {Promise<Array>} - Array of records
   */
  async findBy(field, value, options = {}) {
    const { limit, offset, orderBy, order } = options;

    let query = `SELECT * FROM ${this.tableName} WHERE ${field} = ? AND is_active = 1 AND deleted_at IS NULL`;

    if (orderBy) {
      query += ` ORDER BY ${orderBy} ${order || 'ASC'}`;
    }

    if (limit) {
      query += ` LIMIT ${limit}`;

      if (offset) {
        query += ` OFFSET ${offset}`;
      }
    }

    const result = await dbManager.executeQuery(query, [value]);

    const items = [];
    for (let i = 0; i < result.rows.length; i++) {
      items.push(result.rows.item(i));
    }

    return items;
  }

  /**
   * Search records by field value
   * @param {string} field - Field name
   * @param {string} query - Search query
   * @param {Object} options - Query options
   * @returns {Promise<Array>} - Array of records
   */
  async search(field, query, options = {}) {
    const { limit, offset, orderBy, order } = options;

    let sqlQuery = `SELECT * FROM ${this.tableName} WHERE ${field} LIKE ? AND is_active = 1 AND deleted_at IS NULL`;

    if (orderBy) {
      sqlQuery += ` ORDER BY ${orderBy} ${order || 'ASC'}`;
    }

    if (limit) {
      sqlQuery += ` LIMIT ${limit}`;

      if (offset) {
        sqlQuery += ` OFFSET ${offset}`;
      }
    }

    const result = await dbManager.executeQuery(sqlQuery, [`%${query}%`]);

    const items = [];
    for (let i = 0; i < result.rows.length; i++) {
      items.push(result.rows.item(i));
    }

    return items;
  }

  /**
   * Count records
   * @param {Object} conditions - Query conditions
   * @returns {Promise<number>} - Record count
   */
  async count(conditions = {}) {
    let whereClause = 'is_active = 1 AND deleted_at IS NULL';
    const params = [];

    Object.entries(conditions).forEach(([key, value]) => {
      whereClause += ` AND ${key} = ?`;
      params.push(value);
    });

    const result = await dbManager.executeQuery(
      `SELECT COUNT(*) as count FROM ${this.tableName} WHERE ${whereClause}`,
      params
    );

    return result.rows.item(0).count;
  }
}

/**
 * User data access
 */
class UserDataAccess extends BaseDataAccess {
  constructor() {
    super('User', 'id');
  }

  /**
   * Get user settings
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - User settings
   */
  async getSettings(userId) {
    const result = await dbManager.executeQuery(
      'SELECT setting_key, setting_value FROM UserSetting WHERE user_id = ? AND is_active = 1',
      [userId]
    );

    const settings = {};
    for (let i = 0; i < result.rows.length; i++) {
      const setting = result.rows.item(i);
      settings[setting.setting_key] = setting.setting_value;
    }

    return settings;
  }

  /**
   * Save user setting
   * @param {string} userId - User ID
   * @param {string} key - Setting key
   * @param {string} value - Setting value
   * @returns {Promise<void>}
   */
  async saveSetting(userId, key, value) {
    const now = new Date().toISOString();

    // Check if setting exists
    const result = await dbManager.executeQuery(
      'SELECT id FROM UserSetting WHERE user_id = ? AND setting_key = ?',
      [userId, key]
    );

    if (result.rows.length > 0) {
      // Update existing setting
      await dbManager.executeQuery(
        'UPDATE UserSetting SET setting_value = ?, updated_at = ? WHERE user_id = ? AND setting_key = ?',
        [value, now, userId, key]
      );
    } else {
      // Create new setting
      const settingId = uuidv4();
      await dbManager.executeQuery(
        'INSERT INTO UserSetting (id, user_id, setting_key, setting_value, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)',
        [settingId, userId, key, value, now, now]
      );
    }
  }

  /**
   * Get current user
   * @returns {Promise<Object|null>} - Current user or null
   */
  async getCurrentUser() {
    const result = await dbManager.executeQuery(
      'SELECT * FROM User WHERE is_active = 1 LIMIT 1'
    );

    if (result.rows.length === 0) {
      return null;
    }

    const user = result.rows.item(0);
    user.settings = await this.getSettings(user.id);

    return user;
  }
}

/**
 * Food data access
 */
class FoodDataAccess extends BaseDataAccess {
  constructor() {
    super('Food', 'id');
  }

  /**
   * Get food by barcode
   * @param {string} barcode - Barcode
   * @returns {Promise<Object|null>} - Food or null
   */
  async getByBarcode(barcode) {
    const result = await dbManager.executeQuery(
      'SELECT * FROM Food WHERE barcode = ? AND is_active = 1 AND deleted_at IS NULL',
      [barcode]
    );

    return result.rows.length > 0 ? result.rows.item(0) : null;
  }

  /**
   * Get food nutrients
   * @param {string} foodId - Food ID
   * @returns {Promise<Array>} - Array of nutrients
   */
  async getNutrients(foodId) {
    const result = await dbManager.executeQuery(
      `SELECT fn.*, n.name, n.unit, n.is_macro
       FROM FoodNutrient fn
       JOIN Nutrient n ON fn.nutrient_id = n.id
       WHERE fn.food_id = ? AND fn.is_active = 1 AND n.is_active = 1`,
      [foodId]
    );

    const nutrients = [];
    for (let i = 0; i < result.rows.length; i++) {
      nutrients.push(result.rows.item(i));
    }

    return nutrients;
  }

  /**
   * Save food nutrient
   * @param {string} foodId - Food ID
   * @param {string} nutrientId - Nutrient ID
   * @param {number} amount - Nutrient amount
   * @returns {Promise<void>}
   */
  async saveNutrient(foodId, nutrientId, amount) {
    const now = new Date().toISOString();

    // Check if nutrient exists for this food
    const result = await dbManager.executeQuery(
      'SELECT id FROM FoodNutrient WHERE food_id = ? AND nutrient_id = ?',
      [foodId, nutrientId]
    );

    if (result.rows.length > 0) {
      // Update existing nutrient
      await dbManager.executeQuery(
        'UPDATE FoodNutrient SET amount = ?, updated_at = ? WHERE food_id = ? AND nutrient_id = ?',
        [amount, now, foodId, nutrientId]
      );
    } else {
      // Create new nutrient
      const id = uuidv4();
      await dbManager.executeQuery(
        'INSERT INTO FoodNutrient (id, food_id, nutrient_id, amount, created_at, updated_at, is_active) VALUES (?, ?, ?, ?, ?, ?, 1)',
        [id, foodId, nutrientId, amount, now, now]
      );
    }
  }

  /**
   * Get food ingredients
   * @param {string} foodId - Food ID
   * @returns {Promise<Array>} - Array of ingredients
   */
  async getIngredients(foodId) {
    const result = await dbManager.executeQuery(
      `SELECT fi.*, i.name, i.is_allergen
       FROM FoodIngredient fi
       JOIN Ingredient i ON fi.ingredient_id = i.id
       WHERE fi.food_id = ? AND fi.is_active = 1 AND i.is_active = 1
       ORDER BY fi.order_num`,
      [foodId]
    );

    const ingredients = [];
    for (let i = 0; i < result.rows.length; i++) {
      ingredients.push(result.rows.item(i));
    }

    return ingredients;
  }

  /**
   * Get foods by source
   * @param {string} source - Database source (e.g., 'USDA', 'FoodB')
   * @param {number} limit - Maximum number of results
   * @param {number} offset - Offset for pagination
   * @returns {Promise<Array>} - Array of food objects
   */
  async getBySource(source, limit = 20, offset = 0) {
    const result = await dbManager.executeQuery(
      `SELECT * FROM Food
       WHERE source LIKE ? AND is_active = 1 AND deleted_at IS NULL
       ORDER BY name
       LIMIT ? OFFSET ?`,
      [`${source}%`, limit, offset]
    );

    const foods = [];
    for (let i = 0; i < result.rows.length; i++) {
      const food = result.rows.item(i);
      food.nutrients = await this.getNutrients(food.id);
      food.ingredients = await this.getIngredients(food.id);
      foods.push(food);
    }

    return foods;
  }
}

/**
 * Consumption data access
 */
class ConsumptionDataAccess extends BaseDataAccess {
  constructor() {
    super('Consumption', 'id');
  }

  /**
   * Get consumption items
   * @param {string} consumptionId - Consumption ID
   * @returns {Promise<Array>} - Array of consumption items
   */
  async getItems(consumptionId) {
    const result = await dbManager.executeQuery(
      `SELECT ci.*, f.name as food_name
       FROM ConsumptionItem ci
       JOIN Food f ON ci.food_id = f.id
       WHERE ci.consumption_id = ? AND ci.is_active = 1 AND f.is_active = 1`,
      [consumptionId]
    );

    const items = [];
    for (let i = 0; i < result.rows.length; i++) {
      items.push(result.rows.item(i));
    }

    return items;
  }

  /**
   * Add consumption item
   * @param {string} consumptionId - Consumption ID
   * @param {string} foodId - Food ID
   * @param {number} quantity - Quantity
   * @param {string} unit - Unit
   * @returns {Promise<Object>} - Created consumption item
   */
  async addItem(consumptionId, foodId, quantity, unit = 'g') {
    const now = new Date().toISOString();
    const id = uuidv4();

    await dbManager.executeQuery(
      `INSERT INTO ConsumptionItem (id, consumption_id, food_id, quantity, unit, created_at, updated_at, is_active)
       VALUES (?, ?, ?, ?, ?, ?, ?, 1)`,
      [id, consumptionId, foodId, quantity, unit, now, now]
    );

    const result = await dbManager.executeQuery(
      `SELECT ci.*, f.name as food_name
       FROM ConsumptionItem ci
       JOIN Food f ON ci.food_id = f.id
       WHERE ci.id = ?`,
      [id]
    );

    return result.rows.length > 0 ? result.rows.item(0) : null;
  }

  /**
   * Get consumptions by date
   * @param {string} userId - User ID
   * @param {string} date - Date (YYYY-MM-DD)
   * @returns {Promise<Array>} - Array of consumptions
   */
  async getByDate(userId, date) {
    const result = await dbManager.executeQuery(
      `SELECT c.*
       FROM Consumption c
       WHERE c.user_id = ? AND c.consumption_date = ? AND c.is_active = 1 AND c.deleted_at IS NULL
       ORDER BY c.meal_type`,
      [userId, date]
    );

    const consumptions = [];
    for (let i = 0; i < result.rows.length; i++) {
      const consumption = result.rows.item(i);
      consumption.items = await this.getItems(consumption.id);
      consumptions.push(consumption);
    }

    return consumptions;
  }

  /**
   * Get daily nutrition summary
   * @param {string} userId - User ID
   * @param {string} date - Date (YYYY-MM-DD)
   * @returns {Promise<Object>} - Nutrition summary
   */
  async getDailyNutritionSummary(userId, date) {
    const result = await dbManager.executeQuery(
      `SELECT
         n.id as nutrient_id,
         n.name as nutrient_name,
         n.unit,
         SUM(fn.amount * ci.quantity / 100) as total_amount,
         n.daily_value,
         (SUM(fn.amount * ci.quantity / 100) / n.daily_value * 100) as percent_of_daily_value
       FROM
         Consumption c
       JOIN
         ConsumptionItem ci ON c.id = ci.consumption_id
       JOIN
         Food f ON ci.food_id = f.id
       JOIN
         FoodNutrient fn ON f.id = fn.food_id
       JOIN
         Nutrient n ON fn.nutrient_id = n.id
       WHERE
         c.user_id = ? AND c.consumption_date = ?
         AND c.is_active = 1 AND ci.is_active = 1 AND f.is_active = 1 AND fn.is_active = 1 AND n.is_active = 1
       GROUP BY
         n.id`,
      [userId, date]
    );

    const nutrients = [];
    for (let i = 0; i < result.rows.length; i++) {
      nutrients.push(result.rows.item(i));
    }

    // Get meal type summary
    const mealResult = await dbManager.executeQuery(
      `SELECT
         c.meal_type,
         SUM(fn.amount * ci.quantity / 100) as calories
       FROM
         Consumption c
       JOIN
         ConsumptionItem ci ON c.id = ci.consumption_id
       JOIN
         Food f ON ci.food_id = f.id
       JOIN
         FoodNutrient fn ON f.id = fn.food_id
       WHERE
         c.user_id = ? AND c.consumption_date = ? AND fn.nutrient_id = 'nutrient-calories'
         AND c.is_active = 1 AND ci.is_active = 1 AND f.is_active = 1 AND fn.is_active = 1
       GROUP BY
         c.meal_type`,
      [userId, date]
    );

    const meals = [];
    for (let i = 0; i < mealResult.rows.length; i++) {
      meals.push(mealResult.rows.item(i));
    }

    return {
      date,
      nutrients,
      meals,
      totalCalories: meals.reduce((sum, meal) => sum + meal.calories, 0)
    };
  }
}

/**
 * Nutrient data access
 */
class NutrientDataAccess extends BaseDataAccess {
  constructor() {
    super('Nutrient', 'id');
  }

  /**
   * Get all macronutrients
   * @returns {Promise<Array>} - Array of macronutrients
   */
  async getMacronutrients() {
    const result = await dbManager.executeQuery(
      'SELECT * FROM Nutrient WHERE is_macro = 1 AND is_active = 1 AND deleted_at IS NULL'
    );

    const nutrients = [];
    for (let i = 0; i < result.rows.length; i++) {
      nutrients.push(result.rows.item(i));
    }

    return nutrients;
  }
}

/**
 * Ingredient data access
 */
class IngredientDataAccess extends BaseDataAccess {
  constructor() {
    super('Ingredient', 'id');
  }

  /**
   * Get all allergens
   * @returns {Promise<Array>} - Array of allergens
   */
  async getAllergens() {
    const result = await dbManager.executeQuery(
      'SELECT * FROM Ingredient WHERE is_allergen = 1 AND is_active = 1 AND deleted_at IS NULL'
    );

    const allergens = [];
    for (let i = 0; i < result.rows.length; i++) {
      allergens.push(result.rows.item(i));
    }

    return allergens;
  }
}

/**
 * MealType data access
 */
class MealTypeDataAccess extends BaseDataAccess {
  constructor() {
    super('MealType', 'id');
  }

  /**
   * Get all meal types ordered by display order
   * @returns {Promise<Array>} - Array of meal types
   */
  async getAllOrdered() {
    const result = await dbManager.executeQuery(
      'SELECT * FROM MealType WHERE is_active = 1 AND deleted_at IS NULL ORDER BY display_order'
    );

    const mealTypes = [];
    for (let i = 0; i < result.rows.length; i++) {
      mealTypes.push(result.rows.item(i));
    }

    return mealTypes;
  }
}

// Export data access instances
export const userDataAccess = new UserDataAccess();
export const foodDataAccess = new FoodDataAccess();
export const consumptionDataAccess = new ConsumptionDataAccess();
export const nutrientDataAccess = new NutrientDataAccess();
export const ingredientDataAccess = new IngredientDataAccess();
export const mealTypeDataAccess = new MealTypeDataAccess();

// Export the base class for extension
export { BaseDataAccess };
