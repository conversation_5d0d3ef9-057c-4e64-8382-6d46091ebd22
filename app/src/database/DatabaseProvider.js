/**
 * Database Provider for ZnüniZähler
 * Provides database context and initialization for the app
 */

import React, { createContext, useState, useContext, useEffect } from 'react';
import { initializeDatabase } from '../services/databaseService';

// Create database context
const DatabaseContext = createContext({
  isInitialized: false,
  isLoading: true,
  error: null
});

// Custom hook to use database
export const useDatabase = () => useContext(DatabaseContext);

/**
 * Database Provider Component
 * @param {Object} props - Component props
 * @returns {JSX.Element} - Database provider component
 */
export const DatabaseProvider = ({ children }) => {
  // State for database initialization
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Initialize database
  useEffect(() => {
    const initialize = async () => {
      try {
        setIsLoading(true);
        await initializeDatabase();
        setIsInitialized(true);
        setError(null);
      } catch (err) {
        console.error('Error initializing database:', err);
        setError(err.message || 'Failed to initialize database');
        setIsInitialized(false);
      } finally {
        setIsLoading(false);
      }
    };
    
    initialize();
  }, []);
  
  // Context value
  const contextValue = {
    isInitialized,
    isLoading,
    error
  };
  
  return (
    <DatabaseContext.Provider value={contextValue}>
      {children}
    </DatabaseContext.Provider>
  );
};
