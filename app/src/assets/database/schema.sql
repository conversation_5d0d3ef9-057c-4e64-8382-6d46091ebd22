-- <PERSON><PERSON><PERSON><PERSON>Zähler Mobile App SQLite Implementation
-- This script creates the SQLite database schema for the mobile app

-- Enable foreign keys
PRAGMA foreign_keys = ON;

-- User table
CREATE TABLE IF NOT EXISTS User (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    email TEXT,
    dark_mode_enabled INTEGER DEFAULT 1, -- 1 for true, 0 for false
    preferred_language TEXT DEFAULT 'en',
    measurement_unit TEXT DEFAULT 'metric',
    last_sync TEXT, -- ISO datetime
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    updated_at TEXT NOT NULL DEFAULT (datetime('now')),
    is_active INTEGER DEFAULT 1
);

-- Food table
CREATE TABLE IF NOT EXISTS Food (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    barcode TEXT,
    brand TEXT,
    serving_size REAL,
    serving_unit TEXT,
    image_url TEXT,
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    updated_at TEXT NOT NULL DEFAULT (datetime('now')),
    is_favorite INTEGER DEFAULT 0, -- 1 for true, 0 for false
    is_custom INTEGER DEFAULT 0, -- 1 for true, 0 for false
    created_by_user_id TEXT,
    is_active INTEGER DEFAULT 1,
    deleted_at TEXT,
    FOREIGN KEY (created_by_user_id) REFERENCES User(id) ON DELETE SET NULL
);

-- Create index on barcode for fast lookup
CREATE INDEX IF NOT EXISTS idx_food_barcode ON Food(barcode);

-- Nutrient table
CREATE TABLE IF NOT EXISTS Nutrient (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    unit TEXT NOT NULL,
    is_macro INTEGER NOT NULL, -- 1 for true, 0 for false
    daily_value REAL,
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    updated_at TEXT NOT NULL DEFAULT (datetime('now')),
    is_active INTEGER DEFAULT 1,
    deleted_at TEXT
);

-- FoodNutrient junction table
CREATE TABLE IF NOT EXISTS FoodNutrient (
    id TEXT PRIMARY KEY,
    food_id TEXT NOT NULL,
    nutrient_id TEXT NOT NULL,
    amount REAL NOT NULL,
    unit TEXT,
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    updated_at TEXT NOT NULL DEFAULT (datetime('now')),
    is_active INTEGER DEFAULT 1,
    deleted_at TEXT,
    FOREIGN KEY (food_id) REFERENCES Food(id) ON DELETE CASCADE,
    FOREIGN KEY (nutrient_id) REFERENCES Nutrient(id) ON DELETE CASCADE,
    UNIQUE(food_id, nutrient_id)
);

-- Ingredient table
CREATE TABLE IF NOT EXISTS Ingredient (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    is_allergen INTEGER DEFAULT 0, -- 1 for true, 0 for false
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    updated_at TEXT NOT NULL DEFAULT (datetime('now')),
    is_active INTEGER DEFAULT 1,
    deleted_at TEXT
);

-- FoodIngredient junction table
CREATE TABLE IF NOT EXISTS FoodIngredient (
    id TEXT PRIMARY KEY,
    food_id TEXT NOT NULL,
    ingredient_id TEXT NOT NULL,
    order_num INTEGER,
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    updated_at TEXT NOT NULL DEFAULT (datetime('now')),
    is_active INTEGER DEFAULT 1,
    deleted_at TEXT,
    FOREIGN KEY (food_id) REFERENCES Food(id) ON DELETE CASCADE,
    FOREIGN KEY (ingredient_id) REFERENCES Ingredient(id) ON DELETE CASCADE,
    UNIQUE(food_id, ingredient_id)
);

-- Consumption table
CREATE TABLE IF NOT EXISTS Consumption (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    consumption_date TEXT NOT NULL, -- YYYY-MM-DD
    meal_type TEXT NOT NULL, -- 'breakfast', 'lunch', 'dinner', 'snack'
    notes TEXT,
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    updated_at TEXT NOT NULL DEFAULT (datetime('now')),
    is_active INTEGER DEFAULT 1,
    deleted_at TEXT,
    FOREIGN KEY (user_id) REFERENCES User(id) ON DELETE CASCADE
);

-- Create index on consumption date for fast filtering
CREATE INDEX IF NOT EXISTS idx_consumption_date ON Consumption(consumption_date);

-- ConsumptionItem table
CREATE TABLE IF NOT EXISTS ConsumptionItem (
    id TEXT PRIMARY KEY,
    consumption_id TEXT NOT NULL,
    food_id TEXT NOT NULL,
    quantity REAL NOT NULL,
    unit TEXT,
    notes TEXT,
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    updated_at TEXT NOT NULL DEFAULT (datetime('now')),
    is_active INTEGER DEFAULT 1,
    deleted_at TEXT,
    FOREIGN KEY (consumption_id) REFERENCES Consumption(id) ON DELETE CASCADE,
    FOREIGN KEY (food_id) REFERENCES Food(id) ON DELETE CASCADE
);

-- UserSetting table
CREATE TABLE IF NOT EXISTS UserSetting (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    setting_key TEXT NOT NULL,
    setting_value TEXT,
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    updated_at TEXT NOT NULL DEFAULT (datetime('now')),
    is_active INTEGER DEFAULT 1,
    deleted_at TEXT,
    FOREIGN KEY (user_id) REFERENCES User(id) ON DELETE CASCADE,
    UNIQUE(user_id, setting_key)
);

-- MealType table
CREATE TABLE IF NOT EXISTS MealType (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    display_order INTEGER NOT NULL,
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    updated_at TEXT NOT NULL DEFAULT (datetime('now')),
    is_active INTEGER DEFAULT 1,
    deleted_at TEXT
);

-- Insert default meal types
INSERT OR IGNORE INTO MealType (id, name, display_order) VALUES
('meal-breakfast', 'Breakfast', 1),
('meal-lunch', 'Lunch', 2),
('meal-dinner', 'Dinner', 3),
('meal-snack', 'Snack', 4);

-- Insert default nutrients
INSERT OR IGNORE INTO Nutrient (id, name, unit, is_macro, daily_value) VALUES
('nutrient-calories', 'Calories', 'kcal', 1, 2000),
('nutrient-protein', 'Protein', 'g', 1, 50),
('nutrient-carbs', 'Carbohydrates', 'g', 1, 275),
('nutrient-fat', 'Fat', 'g', 1, 78),
('nutrient-fiber', 'Fiber', 'g', 0, 28),
('nutrient-sugar', 'Sugar', 'g', 0, 50),
('nutrient-sodium', 'Sodium', 'mg', 0, 2300),
('nutrient-cholesterol', 'Cholesterol', 'mg', 0, 300),
('nutrient-sat-fat', 'Saturated Fat', 'g', 0, 20),
('nutrient-trans-fat', 'Trans Fat', 'g', 0, 0);

-- Create a view for daily nutrition summary
CREATE VIEW IF NOT EXISTS DailyNutritionSummary AS
SELECT 
    c.user_id,
    c.consumption_date,
    n.id AS nutrient_id,
    n.name AS nutrient_name,
    n.unit,
    SUM(fn.amount * ci.quantity / 100) AS total_amount,
    n.daily_value,
    (SUM(fn.amount * ci.quantity / 100) / n.daily_value * 100) AS percent_of_daily_value
FROM 
    Consumption c
JOIN 
    ConsumptionItem ci ON c.id = ci.consumption_id
JOIN 
    Food f ON ci.food_id = f.id
JOIN 
    FoodNutrient fn ON f.id = fn.food_id
JOIN 
    Nutrient n ON fn.nutrient_id = n.id
WHERE
    c.is_active = 1 AND ci.is_active = 1 AND f.is_active = 1 AND fn.is_active = 1 AND n.is_active = 1
GROUP BY 
    c.user_id, c.consumption_date, n.id;

-- Create a view for meal type summary
CREATE VIEW IF NOT EXISTS MealTypeSummary AS
SELECT 
    c.user_id,
    c.consumption_date,
    c.meal_type,
    SUM(fn.amount * ci.quantity / 100) AS calories
FROM 
    Consumption c
JOIN 
    ConsumptionItem ci ON c.id = ci.consumption_id
JOIN 
    Food f ON ci.food_id = f.id
JOIN 
    FoodNutrient fn ON f.id = fn.food_id
WHERE 
    fn.nutrient_id = 'nutrient-calories'
    AND c.is_active = 1 AND ci.is_active = 1 AND f.is_active = 1 AND fn.is_active = 1
GROUP BY 
    c.user_id, c.consumption_date, c.meal_type;

-- Create a trigger to update the updated_at timestamp for Food
CREATE TRIGGER IF NOT EXISTS update_food_timestamp
AFTER UPDATE ON Food
BEGIN
    UPDATE Food SET updated_at = datetime('now') WHERE id = NEW.id;
END;

-- Create a trigger to update the updated_at timestamp for Consumption
CREATE TRIGGER IF NOT EXISTS update_consumption_timestamp
AFTER UPDATE ON Consumption
BEGIN
    UPDATE Consumption SET updated_at = datetime('now') WHERE id = NEW.id;
END;

-- Create a trigger to update the updated_at timestamp for ConsumptionItem
CREATE TRIGGER IF NOT EXISTS update_consumption_item_timestamp
AFTER UPDATE ON ConsumptionItem
BEGIN
    UPDATE ConsumptionItem SET updated_at = datetime('now') WHERE id = NEW.id;
END;

-- Create a trigger to update the updated_at timestamp for UserSetting
CREATE TRIGGER IF NOT EXISTS update_user_setting_timestamp
AFTER UPDATE ON UserSetting
BEGIN
    UPDATE UserSetting SET updated_at = datetime('now') WHERE id = NEW.id;
END;

-- Create a trigger to update the updated_at timestamp for User
CREATE TRIGGER IF NOT EXISTS update_user_timestamp
AFTER UPDATE ON User
BEGIN
    UPDATE User SET updated_at = datetime('now') WHERE id = NEW.id;
END;

-- Create a trigger to update the updated_at timestamp for Nutrient
CREATE TRIGGER IF NOT EXISTS update_nutrient_timestamp
AFTER UPDATE ON Nutrient
BEGIN
    UPDATE Nutrient SET updated_at = datetime('now') WHERE id = NEW.id;
END;

-- Create a trigger to update the updated_at timestamp for Ingredient
CREATE TRIGGER IF NOT EXISTS update_ingredient_timestamp
AFTER UPDATE ON Ingredient
BEGIN
    UPDATE Ingredient SET updated_at = datetime('now') WHERE id = NEW.id;
END;

-- Create a trigger to update the updated_at timestamp for FoodNutrient
CREATE TRIGGER IF NOT EXISTS update_food_nutrient_timestamp
AFTER UPDATE ON FoodNutrient
BEGIN
    UPDATE FoodNutrient SET updated_at = datetime('now') WHERE id = NEW.id;
END;

-- Create a trigger to update the updated_at timestamp for FoodIngredient
CREATE TRIGGER IF NOT EXISTS update_food_ingredient_timestamp
AFTER UPDATE ON FoodIngredient
BEGIN
    UPDATE FoodIngredient SET updated_at = datetime('now') WHERE id = NEW.id;
END;

-- Create a trigger to update the updated_at timestamp for MealType
CREATE TRIGGER IF NOT EXISTS update_meal_type_timestamp
AFTER UPDATE ON MealType
BEGIN
    UPDATE MealType SET updated_at = datetime('now') WHERE id = NEW.id;
END;

-- Create a table to track database version
CREATE TABLE IF NOT EXISTS DatabaseVersion (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    version INTEGER NOT NULL,
    applied_at TEXT NOT NULL DEFAULT (datetime('now')),
    description TEXT
);

-- Insert initial version
INSERT OR IGNORE INTO DatabaseVersion (id, version, description) VALUES (1, 1, 'Initial schema');
