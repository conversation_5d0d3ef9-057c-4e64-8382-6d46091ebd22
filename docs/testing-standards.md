# Testing Standards for ZnüniZähler

This document outlines the testing standards and expectations for the ZnüniZähler application.

## Code Coverage Requirements

### Coverage Targets

- **Target Coverage**: 90% overall code coverage
- **Minimum Acceptable**: 80% coverage for any new code
- **Critical Components**: 95% coverage for core functionality (data storage, nutrition calculations)

### Coverage Metrics

We measure code coverage across the following dimensions:
- **Statement Coverage**: 90% of all statements should be executed during tests
- **Branch Coverage**: 90% of all possible branches should be tested
- **Function Coverage**: 90% of all functions should be called during tests

## Testing Approach

### Unit Testing

- All utility functions must have unit tests
- All data transformation logic must have unit tests
- All UI components should have basic rendering tests

### Integration Testing

- All API interactions must have integration tests
- Database operations must have integration tests
- Component interactions should be tested

### End-to-End Testing

- Critical user flows must have end-to-end tests
- Main features should have at least one end-to-end test

## Test Implementation Guidelines

### Test Organization

- Tests should be organized to mirror the structure of the source code
- Test files should be named with `.test.js` or `.spec.js` suffix
- Use descriptive test names that explain the expected behavior

### Test Quality

- Tests should be deterministic (same input always produces same output)
- Avoid testing implementation details when possible
- Mock external dependencies appropriately

## Continuous Integration

- All tests must pass before merging to main branches
- Coverage reports will be generated for each pull request
- Pull requests that decrease coverage below targets will be flagged for review

## Exemptions

In rare cases, certain code may be exempted from coverage requirements:
- Third-party library integrations with limited testability
- Platform-specific code that cannot be tested in CI environment
- Generated code

Exemptions must be documented and approved by the team lead.

## Tools

- Jest for unit and integration testing
- React Testing Library for component testing
- Detox for end-to-end testing on mobile
- Cypress for end-to-end testing on web
- Istanbul for code coverage reporting

## Review Process

Code reviews should include verification of:
- Test coverage meets or exceeds targets
- Tests are meaningful and test the right behaviors
- Edge cases are appropriately covered

## Implementation Timeline

- New features: Must meet coverage targets before merging
- Existing code: Will be brought up to standards incrementally, with priority given to core functionality
